import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:sam03/models/app_models.dart';
import 'package:sam03/services/unified_notification_service.dart';
import 'package:sam03/widgets/unified_app_bar.dart';

/// شاشة الإشعارات الموحدة
class UnifiedNotificationsScreen extends StatefulWidget {
  const UnifiedNotificationsScreen({super.key});

  @override
  State<UnifiedNotificationsScreen> createState() => _UnifiedNotificationsScreenState();
}

class _UnifiedNotificationsScreenState extends State<UnifiedNotificationsScreen> {
  final UnifiedNotificationService _notificationService = UnifiedNotificationService();
  String _selectedFilter = 'all';
  NotificationCategory? _selectedCategory;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: SimpleUnifiedAppBar(
        title: 'الإشعارات',
        actions: [
          IconButton(
            icon: const Icon(Icons.mark_email_read),
            onPressed: _markAllAsRead,
            tooltip: 'تحديد الكل كمقروء',
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) {
              if (value == 'clear_all') {
                _showClearAllDialog();
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'clear_all',
                child: Row(
                  children: [
                    Icon(Icons.clear_all, color: Colors.red),
                    SizedBox(width: 8),
                    Text('حذف جميع الإشعارات'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // فلاتر الإشعارات
          _buildFilterSection(),
          
          // قائمة الإشعارات
          Expanded(
            child: _buildNotificationsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // فلتر حسب الحالة
          Row(
            children: [
              Expanded(
                child: _buildFilterChip('all', 'الكل', Icons.notifications),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildFilterChip('unread', 'غير مقروء', Icons.mark_email_unread),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildFilterChip('critical', 'حرجة', Icons.priority_high),
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          // فلتر حسب الفئة
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildCategoryChip(null, 'جميع الفئات', Icons.all_inclusive),
                const SizedBox(width: 8),
                _buildCategoryChip(NotificationCategory.irrigation, 'الري', Icons.water_drop),
                const SizedBox(width: 8),
                _buildCategoryChip(NotificationCategory.disease, 'الأمراض', Icons.local_hospital),
                const SizedBox(width: 8),
                _buildCategoryChip(NotificationCategory.market, 'السوق', Icons.store),
                const SizedBox(width: 8),
                _buildCategoryChip(NotificationCategory.system, 'النظام', Icons.settings),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String value, String label, IconData icon) {
    final isSelected = _selectedFilter == value;
    final notifications = _getFilteredNotifications();
    
    return FilterChip(
      selected: isSelected,
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16),
          const SizedBox(width: 4),
          Text(label),
          if (notifications.isNotEmpty) ...[
            const SizedBox(width: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: isSelected ? Colors.white : Theme.of(context).primaryColor,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                '${notifications.length}',
                style: TextStyle(
                  color: isSelected ? Theme.of(context).primaryColor : Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ],
      ),
      onSelected: (selected) {
        if (selected) {
          setState(() {
            _selectedFilter = value;
          });
        }
      },
    );
  }

  Widget _buildCategoryChip(NotificationCategory? category, String label, IconData icon) {
    final isSelected = _selectedCategory == category;
    
    return FilterChip(
      selected: isSelected,
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16),
          const SizedBox(width: 4),
          Text(label),
        ],
      ),
      onSelected: (selected) {
        setState(() {
          _selectedCategory = selected ? category : null;
        });
      },
    );
  }

  Widget _buildNotificationsList() {
    final notifications = _getFilteredNotifications();
    
    if (notifications.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.separated(
      padding: const EdgeInsets.all(16.0),
      itemCount: notifications.length,
      separatorBuilder: (context, index) => const SizedBox(height: 8),
      itemBuilder: (context, index) {
        final notification = notifications[index];
        return _buildNotificationCard(notification);
      },
    );
  }

  Widget _buildEmptyState() {
    String message;
    IconData icon;
    
    switch (_selectedFilter) {
      case 'unread':
        message = 'لا توجد إشعارات غير مقروءة';
        icon = Icons.mark_email_read;
        break;
      case 'critical':
        message = 'لا توجد إشعارات حرجة';
        icon = Icons.check_circle;
        break;
      default:
        message = 'لا توجد إشعارات';
        icon = Icons.notifications_none;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationCard(UnifiedNotification notification) {
    final color = _notificationService.getNotificationColor(notification.type);
    final icon = _notificationService.getNotificationIcon(notification.type);
    final categoryIcon = _notificationService.getCategoryIcon(notification.category);
    
    return Card(
      elevation: notification.isRead ? 1 : 3,
      child: InkWell(
        onTap: () => _onNotificationTap(notification),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: notification.isRead 
                ? null 
                : Border.all(color: color.withOpacity(0.3), width: 2),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس الإشعار
              Row(
                children: [
                  CircleAvatar(
                    backgroundColor: color.withOpacity(0.1),
                    radius: 20,
                    child: Icon(icon, color: color, size: 20),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(categoryIcon, size: 16, color: Colors.grey),
                            const SizedBox(width: 4),
                            Text(
                              _getCategoryName(notification.category),
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                        Text(
                          notification.title,
                          style: TextStyle(
                            fontWeight: notification.isRead 
                                ? FontWeight.normal 
                                : FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        _formatTime(notification.timestamp),
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 12,
                        ),
                      ),
                      if (notification.priority == NotificationPriority.critical ||
                          notification.priority == NotificationPriority.high)
                        Container(
                          margin: const EdgeInsets.only(top: 4),
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: notification.priority == NotificationPriority.critical
                                ? Colors.red
                                : Colors.orange,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            notification.priority == NotificationPriority.critical
                                ? 'حرج'
                                : 'مهم',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // محتوى الإشعار
              Text(
                notification.message,
                style: TextStyle(
                  color: notification.isRead 
                      ? Colors.grey.shade700 
                      : Colors.black87,
                ),
              ),
              
              // أزرار الإجراءات
              if (notification.actionRequired) ...[
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton.icon(
                      onPressed: () => _handleNotificationAction(notification),
                      icon: const Icon(Icons.open_in_new, size: 16),
                      label: const Text('عرض التفاصيل'),
                      style: TextButton.styleFrom(
                        foregroundColor: color,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  List<UnifiedNotification> _getFilteredNotifications() {
    var notifications = _notificationService.notifications;
    
    // فلترة حسب الحالة
    switch (_selectedFilter) {
      case 'unread':
        notifications = notifications.where((n) => !n.isRead).toList();
        break;
      case 'critical':
        notifications = notifications.where((n) => 
            n.priority == NotificationPriority.critical ||
            n.priority == NotificationPriority.high
        ).toList();
        break;
    }
    
    // فلترة حسب الفئة
    if (_selectedCategory != null) {
      notifications = notifications.where((n) => n.category == _selectedCategory).toList();
    }
    
    return notifications;
  }

  String _getCategoryName(NotificationCategory category) {
    switch (category) {
      case NotificationCategory.irrigation:
        return 'نظام الري';
      case NotificationCategory.disease:
        return 'تشخيص الأمراض';
      case NotificationCategory.market:
        return 'السوق الزراعي';
      case NotificationCategory.general:
        return 'عام';
      case NotificationCategory.system:
        return 'النظام';
    }
  }

  String _formatTime(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${timestamp.day}/${timestamp.month}';
    }
  }

  void _onNotificationTap(UnifiedNotification notification) {
    if (!notification.isRead) {
      _notificationService.markAsRead(notification.id);
      setState(() {});
    }
  }

  void _handleNotificationAction(UnifiedNotification notification) {
    // هنا يمكن إضافة منطق للتعامل مع الإجراءات المطلوبة
    // مثل الانتقال لشاشة النظام أو تشغيل الري
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('إجراء للإشعار: ${notification.title}'),
        action: SnackBarAction(
          label: 'إغلاق',
          onPressed: () {},
        ),
      ),
    );
  }

  void _markAllAsRead() {
    _notificationService.markAllAsRead();
    setState(() {});
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديد جميع الإشعارات كمقروءة'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showClearAllDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف جميع الإشعارات'),
        content: const Text('هل أنت متأكد من حذف جميع الإشعارات؟ لا يمكن التراجع عن هذا الإجراء.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              _notificationService.clearAllNotifications();
              setState(() {});
              Navigator.pop(context);
              
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم حذف جميع الإشعارات'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
