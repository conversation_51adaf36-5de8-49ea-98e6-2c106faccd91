import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:sam03/bloc/irrigation_bloc.dart';
import 'package:sam03/bloc/auth_bloc.dart';
import 'package:sam03/core/constants/app_constants.dart';
import 'package:sam03/models/app_models.dart';

/// شاشة إضافة نظام ري جديد
class AddIrrigationSystemScreen extends StatefulWidget {
  const AddIrrigationSystemScreen({super.key});

  @override
  State<AddIrrigationSystemScreen> createState() => _AddIrrigationSystemScreenState();
}

class _AddIrrigationSystemScreenState extends State<AddIrrigationSystemScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _serialNumberController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _locationController = TextEditingController();
  final _cropTypeController = TextEditingController();
  
  String _selectedType = AppConstants.irrigationTypeDrip;
  bool _isLoading = false;

  @override
  void dispose() {
    _nameController.dispose();
    _serialNumberController.dispose();
    _descriptionController.dispose();
    _locationController.dispose();
    _cropTypeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إضافة نظام ري جديد'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: BlocListener<IrrigationBloc, IrrigationState>(
        listener: (context, state) {
          if (state is IrrigationStateSystemAdded) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم إضافة النظام بنجاح'),
                backgroundColor: Colors.green,
              ),
            );
            Navigator.pop(context);
          } else if (state is IrrigationStateError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
            setState(() {
              _isLoading = false;
            });
          }
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // معلومات أساسية
                _buildSectionTitle('المعلومات الأساسية'),
                const SizedBox(height: 16),
                
                _buildTextField(
                  controller: _nameController,
                  label: 'اسم النظام',
                  hint: 'مثال: نظام ري الحديقة الأمامية',
                  icon: Icons.label,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'يرجى إدخال اسم النظام';
                    }
                    if (value.trim().length < 3) {
                      return 'يجب أن يكون الاسم 3 أحرف على الأقل';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                _buildTextField(
                  controller: _serialNumberController,
                  label: 'الرقم التسلسلي',
                  hint: 'مثال: IRR-001-2024',
                  icon: Icons.qr_code,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'يرجى إدخال الرقم التسلسلي';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // نوع النظام
                _buildTypeSelector(),
                const SizedBox(height: 24),

                // معلومات إضافية
                _buildSectionTitle('معلومات إضافية'),
                const SizedBox(height: 16),

                _buildTextField(
                  controller: _locationController,
                  label: 'الموقع',
                  hint: 'مثال: الحديقة الأمامية - القطاع الشرقي',
                  icon: Icons.location_on,
                  required: false,
                ),
                const SizedBox(height: 16),

                _buildTextField(
                  controller: _cropTypeController,
                  label: 'نوع المحصول',
                  hint: 'مثال: طماطم، خيار، ورود',
                  icon: FontAwesomeIcons.seedling,
                  required: false,
                ),
                const SizedBox(height: 16),

                _buildTextField(
                  controller: _descriptionController,
                  label: 'الوصف',
                  hint: 'وصف مختصر للنظام وخصائصه',
                  icon: Icons.description,
                  maxLines: 3,
                  required: false,
                ),
                const SizedBox(height: 32),

                // أزرار الإجراءات
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: _isLoading ? null : () => Navigator.pop(context),
                        child: const Text('إلغاء'),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _addSystem,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(context).primaryColor,
                          foregroundColor: Colors.white,
                        ),
                        child: _isLoading
                            ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              )
                            : const Text('إضافة النظام'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: Colors.black87,
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    String? Function(String?)? validator,
    int maxLines = 1,
    bool required = true,
  }) {
    return TextFormField(
      controller: controller,
      maxLines: maxLines,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        filled: true,
        fillColor: Colors.grey.shade50,
      ),
      validator: required ? validator : null,
    );
  }

  Widget _buildTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'نوع نظام الري',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              _buildTypeOption(
                AppConstants.irrigationTypeDrip,
                'ري بالتنقيط',
                'نظام ري دقيق وموفر للمياه',
                FontAwesomeIcons.droplet,
                Colors.blue,
              ),
              const Divider(height: 1),
              _buildTypeOption(
                AppConstants.irrigationTypeSprinkler,
                'ري بالرش',
                'نظام ري واسع المدى للمساحات الكبيرة',
                FontAwesomeIcons.shower,
                Colors.green,
              ),
              const Divider(height: 1),
              _buildTypeOption(
                AppConstants.irrigationTypeMicro,
                'ري دقيق',
                'نظام ري متخصص للنباتات الحساسة',
                FontAwesomeIcons.seedling,
                Colors.orange,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTypeOption(
    String value,
    String title,
    String description,
    IconData icon,
    Color color,
  ) {
    final isSelected = _selectedType == value;
    
    return InkWell(
      onTap: () {
        setState(() {
          _selectedType = value;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Radio<String>(
              value: value,
              groupValue: _selectedType,
              onChanged: (String? newValue) {
                if (newValue != null) {
                  setState(() {
                    _selectedType = newValue;
                  });
                }
              },
              activeColor: color,
            ),
            const SizedBox(width: 12),
            CircleAvatar(
              backgroundColor: color.withOpacity(0.1),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                      color: isSelected ? color : Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _addSystem() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final authState = context.read<AuthBloc>().state;
    if (authState is! AuthStateAuthenticated) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب تسجيل الدخول أولاً'),
          backgroundColor: Colors.red,
        ),
      );
      setState(() {
        _isLoading = false;
      });
      return;
    }

    context.read<IrrigationBloc>().add(
      IrrigationEventAddSystem(
        userId: authState.user.id,
        name: _nameController.text.trim(),
        type: _selectedType,
        serialNumber: _serialNumberController.text.trim(),
        description: _descriptionController.text.trim().isEmpty 
            ? null 
            : _descriptionController.text.trim(),
        location: _locationController.text.trim().isEmpty 
            ? null 
            : _locationController.text.trim(),
        cropType: _cropTypeController.text.trim().isEmpty 
            ? null 
            : _cropTypeController.text.trim(),
      ),
    );
  }
}
