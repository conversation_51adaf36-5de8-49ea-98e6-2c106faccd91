import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:sam03/bloc/irrigation_bloc.dart';
import 'package:sam03/bloc/auth_bloc.dart';
import 'package:sam03/core/constants/app_constants.dart';
import 'package:sam03/models/app_models.dart';
import 'package:sam03/widgets/unified_app_bar.dart';

/// شاشة ربط نظام ري موجود
class LinkIrrigationSystemScreen extends StatefulWidget {
  const LinkIrrigationSystemScreen({super.key});

  @override
  State<LinkIrrigationSystemScreen> createState() =>
      _LinkIrrigationSystemScreenState();
}

class _LinkIrrigationSystemScreenState
    extends State<LinkIrrigationSystemScreen> {
  final _formKey = GlobalKey<FormState>();
  final _fullNameController = TextEditingController();
  final _serialNumberController = TextEditingController();

  bool _isLoading = false;
  IrrigationSystem? _foundSystem;

  @override
  void dispose() {
    _fullNameController.dispose();
    _serialNumberController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const SimpleUnifiedAppBar(
        title: 'ربط نظام ري موجود',
      ),
      body: BlocListener<IrrigationBloc, IrrigationState>(
        listener: (context, state) {
          if (state is IrrigationStateSystemLinked) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم ربط النظام بنجاح'),
                backgroundColor: Colors.green,
              ),
            );
            Navigator.pop(context);
          } else if (state is IrrigationStateSystemFound) {
            setState(() {
              _foundSystem = state.system;
              _isLoading = false;
            });
          } else if (state is IrrigationStateError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
            setState(() {
              _isLoading = false;
            });
          }
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // شرح العملية
                _buildInfoCard(),
                const SizedBox(height: 24),

                // معلومات المستخدم
                _buildSectionTitle('معلومات المستخدم'),
                const SizedBox(height: 16),

                _buildTextField(
                  controller: _fullNameController,
                  label: 'الاسم الكامل',
                  hint: 'أدخل اسمك الكامل كما هو مسجل',
                  icon: Icons.person,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'يرجى إدخال الاسم الكامل';
                    }
                    if (value.trim().length < 3) {
                      return 'يجب أن يكون الاسم 3 أحرف على الأقل';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                _buildTextField(
                  controller: _serialNumberController,
                  label: 'الرقم التسلسلي للنظام',
                  hint: 'مثال: IRR-001-2024',
                  icon: Icons.qr_code_scanner,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'يرجى إدخال الرقم التسلسلي';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),

                // عرض النظام المكتشف
                if (_foundSystem != null) ...[
                  _buildFoundSystemCard(),
                  const SizedBox(height: 24),
                ],

                // أزرار الإجراءات
                _buildActionButtons(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      color: Colors.blue.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.blue.shade700,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'كيفية ربط النظام',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              '1. أدخل اسمك الكامل كما هو مسجل في النظام\n'
              '2. أدخل الرقم التسلسلي المكتوب على جهاز الري\n'
              '3. اضغط "البحث عن النظام" للتحقق من البيانات\n'
              '4. إذا تم العثور على النظام، اضغط "ربط النظام"',
              style: TextStyle(
                fontSize: 14,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: Colors.black87,
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    String? Function(String?)? validator,
    int maxLines = 1,
  }) {
    return TextFormField(
      controller: controller,
      maxLines: maxLines,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        filled: true,
        fillColor: Colors.grey.shade50,
      ),
      validator: validator,
    );
  }

  Widget _buildFoundSystemCard() {
    if (_foundSystem == null) return const SizedBox.shrink();

    return Card(
      color: Colors.green.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: Colors.green.shade700,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'تم العثور على النظام!',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.green.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildSystemInfo('اسم النظام', _foundSystem!.name),
            _buildSystemInfo('النوع', _getSystemTypeName(_foundSystem!.type)),
            _buildSystemInfo('الرقم التسلسلي', _foundSystem!.serialNumber),
            if (_foundSystem!.location != null)
              _buildSystemInfo('الموقع', _foundSystem!.location!),
            if (_foundSystem!.description != null)
              _buildSystemInfo('الوصف', _foundSystem!.description!),
          ],
        ),
      ),
    );
  }

  Widget _buildSystemInfo(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        // زر البحث
        if (_foundSystem == null) ...[
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _isLoading ? null : _searchForSystem,
              icon: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(Icons.search),
              label: Text(_isLoading ? 'جاري البحث...' : 'البحث عن النظام'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
          ),
        ],

        // أزرار الربط والإلغاء
        if (_foundSystem != null) ...[
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: _resetSearch,
                  child: const Text('بحث مرة أخرى'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _linkSystem,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text('ربط النظام'),
                ),
              ),
            ],
          ),
        ],

        const SizedBox(height: 16),

        // زر الإلغاء
        SizedBox(
          width: double.infinity,
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ),
      ],
    );
  }

  String _getSystemTypeName(String type) {
    return AppConstants.irrigationTypeNames[type] ?? 'غير محدد';
  }

  void _searchForSystem() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final authState = context.read<AuthBloc>().state;
    if (authState is! AuthStateAuthenticated) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب تسجيل الدخول أولاً'),
          backgroundColor: Colors.red,
        ),
      );
      setState(() {
        _isLoading = false;
      });
      return;
    }

    context.read<IrrigationBloc>().add(
          IrrigationEventSearchSystem(
            userId: authState.user.id,
            fullName: _fullNameController.text.trim(),
            serialNumber: _serialNumberController.text.trim(),
          ),
        );
  }

  void _linkSystem() async {
    if (_foundSystem == null) return;

    setState(() {
      _isLoading = true;
    });

    final authState = context.read<AuthBloc>().state;
    if (authState is! AuthStateAuthenticated) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب تسجيل الدخول أولاً'),
          backgroundColor: Colors.red,
        ),
      );
      setState(() {
        _isLoading = false;
      });
      return;
    }

    context.read<IrrigationBloc>().add(
          IrrigationEventLinkSystem(
            userId: authState.user.id,
            systemId: _foundSystem!.id,
          ),
        );
  }

  void _resetSearch() {
    setState(() {
      _foundSystem = null;
      _isLoading = false;
    });
  }
}
