import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:sam03/services/unified_notification_service.dart';
import 'package:sam03/screens/unified_notifications_screen.dart';

/// شريط التطبيق الموحد لجميع الشاشات
class UnifiedAppBar extends StatefulWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool showNotifications;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? elevation;
  final bool centerTitle;

  const UnifiedAppBar({
    super.key,
    required this.title,
    this.actions,
    this.leading,
    this.showNotifications = true,
    this.showBackButton = true,
    this.onBackPressed,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation,
    this.centerTitle = true,
  });

  @override
  State<UnifiedAppBar> createState() => _UnifiedAppBarState();

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class _UnifiedAppBarState extends State<UnifiedAppBar> {
  final UnifiedNotificationService _notificationService = UnifiedNotificationService();
  int _unreadCount = 0;

  @override
  void initState() {
    super.initState();
    _updateUnreadCount();
    _notificationService.addListener(_onNotificationReceived);
  }

  @override
  void dispose() {
    _notificationService.removeListener(_onNotificationReceived);
    super.dispose();
  }

  void _onNotificationReceived(notification) {
    _updateUnreadCount();
  }

  void _updateUnreadCount() {
    if (mounted) {
      setState(() {
        _unreadCount = _notificationService.unreadCount;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AppBar(
      title: Text(
        widget.title,
        style: const TextStyle(
          fontFamily: 'Tajawal',
          fontWeight: FontWeight.bold,
        ),
      ),
      leading: widget.leading ?? (widget.showBackButton && Navigator.canPop(context)
          ? IconButton(
              icon: const Icon(Icons.arrow_back_ios),
              onPressed: widget.onBackPressed ?? () => Navigator.pop(context),
            )
          : const Icon(FontAwesomeIcons.seedling, color: Color(0xFF2E7D32))),
      backgroundColor: widget.backgroundColor ?? theme.primaryColor,
      foregroundColor: widget.foregroundColor ?? Colors.white,
      elevation: widget.elevation ?? 1,
      centerTitle: widget.centerTitle,
      actions: [
        // أيقونة الإشعارات
        if (widget.showNotifications) _buildNotificationButton(),
        
        // الإجراءات المخصصة
        if (widget.actions != null) ...widget.actions!,
      ],
    );
  }

  Widget _buildNotificationButton() {
    return Stack(
      children: [
        IconButton(
          icon: const Icon(Icons.notifications),
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const UnifiedNotificationsScreen(),
              ),
            );
          },
          tooltip: 'الإشعارات',
        ),
        if (_unreadCount > 0)
          Positioned(
            right: 8,
            top: 8,
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(10),
              ),
              constraints: const BoxConstraints(
                minWidth: 16,
                minHeight: 16,
              ),
              child: Text(
                _unreadCount > 99 ? '99+' : '$_unreadCount',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
      ],
    );
  }
}

/// شريط تطبيق مبسط للشاشات الفرعية
class SimpleUnifiedAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final VoidCallback? onBackPressed;

  const SimpleUnifiedAppBar({
    super.key,
    required this.title,
    this.actions,
    this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    return UnifiedAppBar(
      title: title,
      actions: actions,
      onBackPressed: onBackPressed,
      showNotifications: false,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// شريط تطبيق للشاشة الرئيسية
class HomeUnifiedAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final VoidCallback? onSearchPressed;
  final VoidCallback? onSettingsPressed;

  const HomeUnifiedAppBar({
    super.key,
    required this.title,
    this.onSearchPressed,
    this.onSettingsPressed,
  });

  @override
  Widget build(BuildContext context) {
    return UnifiedAppBar(
      title: title,
      showBackButton: false,
      leading: const Icon(FontAwesomeIcons.seedling, color: Color(0xFF2E7D32)),
      actions: [
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: onSearchPressed ?? () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('البحث قيد التطوير')),
            );
          },
          tooltip: 'البحث',
        ),
        IconButton(
          icon: const Icon(Icons.settings),
          onPressed: onSettingsPressed ?? () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('الإعدادات قيد التطوير')),
            );
          },
          tooltip: 'الإعدادات',
        ),
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// شريط تطبيق لشاشات نظام الري
class IrrigationUnifiedAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final VoidCallback? onRefreshPressed;
  final List<Widget>? additionalActions;

  const IrrigationUnifiedAppBar({
    super.key,
    required this.title,
    this.onRefreshPressed,
    this.additionalActions,
  });

  @override
  Widget build(BuildContext context) {
    return UnifiedAppBar(
      title: title,
      actions: [
        if (onRefreshPressed != null)
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: onRefreshPressed,
            tooltip: 'تحديث',
          ),
        if (additionalActions != null) ...additionalActions!,
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// شريط تطبيق لشاشات تشخيص الأمراض
class DiseaseUnifiedAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final VoidCallback? onHelpPressed;
  final List<Widget>? additionalActions;

  const DiseaseUnifiedAppBar({
    super.key,
    required this.title,
    this.onHelpPressed,
    this.additionalActions,
  });

  @override
  Widget build(BuildContext context) {
    return UnifiedAppBar(
      title: title,
      actions: [
        if (onHelpPressed != null)
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: onHelpPressed,
            tooltip: 'مساعدة',
          ),
        if (additionalActions != null) ...additionalActions!,
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// شريط تطبيق للسوق الزراعي
class MarketUnifiedAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final VoidCallback? onCartPressed;
  final VoidCallback? onFilterPressed;
  final int cartItemCount;

  const MarketUnifiedAppBar({
    super.key,
    required this.title,
    this.onCartPressed,
    this.onFilterPressed,
    this.cartItemCount = 0,
  });

  @override
  Widget build(BuildContext context) {
    return UnifiedAppBar(
      title: title,
      actions: [
        if (onFilterPressed != null)
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: onFilterPressed,
            tooltip: 'فلترة',
          ),
        if (onCartPressed != null)
          Stack(
            children: [
              IconButton(
                icon: const Icon(Icons.shopping_cart),
                onPressed: onCartPressed,
                tooltip: 'السلة',
              ),
              if (cartItemCount > 0)
                Positioned(
                  right: 8,
                  top: 8,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 16,
                      minHeight: 16,
                    ),
                    child: Text(
                      cartItemCount > 99 ? '99+' : '$cartItemCount',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
            ],
          ),
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
