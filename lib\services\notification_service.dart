import 'package:flutter/material.dart';
import 'package:sam03/models/app_models.dart';
import 'package:sam03/core/constants/app_constants.dart';

/// خدمة الإشعارات الذكية لنظام الري
class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final List<IrrigationNotification> _notifications = [];
  final List<Function(IrrigationNotification)> _listeners = [];

  /// إضافة مستمع للإشعارات
  void addListener(Function(IrrigationNotification) listener) {
    _listeners.add(listener);
  }

  /// إزالة مستمع للإشعارات
  void removeListener(Function(IrrigationNotification) listener) {
    _listeners.remove(listener);
  }

  /// إرسال إشعار جديد
  void sendNotification(IrrigationNotification notification) {
    _notifications.insert(0, notification);
    
    // الاحتفاظ بآخر 50 إشعار فقط
    if (_notifications.length > 50) {
      _notifications.removeRange(50, _notifications.length);
    }

    // إشعار جميع المستمعين
    for (final listener in _listeners) {
      listener(notification);
    }
  }

  /// الحصول على جميع الإشعارات
  List<IrrigationNotification> get notifications => List.unmodifiable(_notifications);

  /// الحصول على الإشعارات غير المقروءة
  List<IrrigationNotification> get unreadNotifications =>
      _notifications.where((n) => !n.isRead).toList();

  /// عدد الإشعارات غير المقروءة
  int get unreadCount => unreadNotifications.length;

  /// تحديد إشعار كمقروء
  void markAsRead(String notificationId) {
    final index = _notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      _notifications[index] = _notifications[index].copyWith(isRead: true);
    }
  }

  /// تحديد جميع الإشعارات كمقروءة
  void markAllAsRead() {
    for (int i = 0; i < _notifications.length; i++) {
      _notifications[i] = _notifications[i].copyWith(isRead: true);
    }
  }

  /// حذف إشعار
  void deleteNotification(String notificationId) {
    _notifications.removeWhere((n) => n.id == notificationId);
  }

  /// حذف جميع الإشعارات
  void clearAllNotifications() {
    _notifications.clear();
  }

  /// فحص حالة النظام وإرسال إشعارات تلقائية
  void checkSystemStatus(IrrigationSystem system) {
    final now = DateTime.now();
    
    // فحص رطوبة التربة
    _checkSoilMoisture(system, now);
    
    // فحص مستوى المياه
    _checkWaterLevel(system, now);
    
    // فحص مستوى البطارية
    _checkBatteryLevel(system, now);
    
    // فحص درجة الحرارة
    _checkTemperature(system, now);
    
    // فحص آخر تحديث للحساسات
    _checkSensorUpdate(system, now);
  }

  void _checkSoilMoisture(IrrigationSystem system, DateTime now) {
    final moisture = system.sensors.soilMoisture;
    
    if (moisture < AppConstants.moistureThresholdLow) {
      sendNotification(IrrigationNotification(
        id: '${system.id}_moisture_low_${now.millisecondsSinceEpoch}',
        systemId: system.id,
        systemName: system.name,
        type: NotificationType.warning,
        title: 'رطوبة التربة منخفضة',
        message: 'رطوبة التربة في ${system.name} منخفضة (${(moisture * 100).toInt()}%). يُنصح بالري.',
        timestamp: now,
        isRead: false,
        actionRequired: true,
        priority: NotificationPriority.high,
      ));
    } else if (moisture > 0.8) {
      sendNotification(IrrigationNotification(
        id: '${system.id}_moisture_high_${now.millisecondsSinceEpoch}',
        systemId: system.id,
        systemName: system.name,
        type: NotificationType.info,
        title: 'رطوبة التربة عالية',
        message: 'رطوبة التربة في ${system.name} عالية (${(moisture * 100).toInt()}%). قد لا تحتاج للري.',
        timestamp: now,
        isRead: false,
        actionRequired: false,
        priority: NotificationPriority.low,
      ));
    }
  }

  void _checkWaterLevel(IrrigationSystem system, DateTime now) {
    final waterLevel = system.sensors.waterLevel;
    
    if (waterLevel < AppConstants.waterLevelThresholdLow) {
      sendNotification(IrrigationNotification(
        id: '${system.id}_water_low_${now.millisecondsSinceEpoch}',
        systemId: system.id,
        systemName: system.name,
        type: NotificationType.error,
        title: 'مستوى المياه منخفض',
        message: 'مستوى المياه في ${system.name} منخفض جداً (${(waterLevel * 100).toInt()}%). يرجى إعادة التعبئة.',
        timestamp: now,
        isRead: false,
        actionRequired: true,
        priority: NotificationPriority.critical,
      ));
    } else if (waterLevel < AppConstants.waterLevelThresholdMedium) {
      sendNotification(IrrigationNotification(
        id: '${system.id}_water_medium_${now.millisecondsSinceEpoch}',
        systemId: system.id,
        systemName: system.name,
        type: NotificationType.warning,
        title: 'مستوى المياه متوسط',
        message: 'مستوى المياه في ${system.name} متوسط (${(waterLevel * 100).toInt()}%). فكر في إعادة التعبئة قريباً.',
        timestamp: now,
        isRead: false,
        actionRequired: false,
        priority: NotificationPriority.medium,
      ));
    }
  }

  void _checkBatteryLevel(IrrigationSystem system, DateTime now) {
    final batteryLevel = system.sensors.batteryLevel;
    
    if (batteryLevel < AppConstants.batteryThresholdLow) {
      sendNotification(IrrigationNotification(
        id: '${system.id}_battery_low_${now.millisecondsSinceEpoch}',
        systemId: system.id,
        systemName: system.name,
        type: NotificationType.error,
        title: 'البطارية منخفضة',
        message: 'مستوى البطارية في ${system.name} منخفض (${(batteryLevel * 100).toInt()}%). يرجى الشحن أو الاستبدال.',
        timestamp: now,
        isRead: false,
        actionRequired: true,
        priority: NotificationPriority.high,
      ));
    } else if (batteryLevel < AppConstants.batteryThresholdMedium) {
      sendNotification(IrrigationNotification(
        id: '${system.id}_battery_medium_${now.millisecondsSinceEpoch}',
        systemId: system.id,
        systemName: system.name,
        type: NotificationType.warning,
        title: 'البطارية متوسطة',
        message: 'مستوى البطارية في ${system.name} متوسط (${(batteryLevel * 100).toInt()}%). فكر في الشحن قريباً.',
        timestamp: now,
        isRead: false,
        actionRequired: false,
        priority: NotificationPriority.medium,
      ));
    }
  }

  void _checkTemperature(IrrigationSystem system, DateTime now) {
    final temperature = system.sensors.temperature;
    
    if (temperature > AppConstants.temperatureThresholdWarm) {
      sendNotification(IrrigationNotification(
        id: '${system.id}_temp_high_${now.millisecondsSinceEpoch}',
        systemId: system.id,
        systemName: system.name,
        type: NotificationType.warning,
        title: 'درجة الحرارة مرتفعة',
        message: 'درجة الحرارة في ${system.name} مرتفعة (${temperature.toStringAsFixed(1)}°م). قد تحتاج النباتات لري إضافي.',
        timestamp: now,
        isRead: false,
        actionRequired: false,
        priority: NotificationPriority.medium,
      ));
    } else if (temperature < AppConstants.temperatureThresholdCold) {
      sendNotification(IrrigationNotification(
        id: '${system.id}_temp_low_${now.millisecondsSinceEpoch}',
        systemId: system.id,
        systemName: system.name,
        type: NotificationType.info,
        title: 'درجة الحرارة منخفضة',
        message: 'درجة الحرارة في ${system.name} منخفضة (${temperature.toStringAsFixed(1)}°م). قد تحتاج النباتات لحماية من البرد.',
        timestamp: now,
        isRead: false,
        actionRequired: false,
        priority: NotificationPriority.low,
      ));
    }
  }

  void _checkSensorUpdate(IrrigationSystem system, DateTime now) {
    final lastUpdate = system.sensors.lastUpdate;
    final timeDifference = now.difference(lastUpdate);
    
    if (timeDifference.inHours > 2) {
      sendNotification(IrrigationNotification(
        id: '${system.id}_sensor_offline_${now.millisecondsSinceEpoch}',
        systemId: system.id,
        systemName: system.name,
        type: NotificationType.error,
        title: 'الحساسات غير متصلة',
        message: 'لم يتم تحديث بيانات الحساسات في ${system.name} منذ ${timeDifference.inHours} ساعة. تحقق من الاتصال.',
        timestamp: now,
        isRead: false,
        actionRequired: true,
        priority: NotificationPriority.high,
      ));
    }
  }

  /// إرسال إشعار بدء الري
  void notifyIrrigationStarted(IrrigationSystem system, IrrigationTrigger trigger) {
    final triggerName = _getTriggerName(trigger);
    sendNotification(IrrigationNotification(
      id: '${system.id}_irrigation_started_${DateTime.now().millisecondsSinceEpoch}',
      systemId: system.id,
      systemName: system.name,
      type: NotificationType.success,
      title: 'بدء الري',
      message: 'تم بدء الري في ${system.name} ($triggerName)',
      timestamp: DateTime.now(),
      isRead: false,
      actionRequired: false,
      priority: NotificationPriority.medium,
    ));
  }

  /// إرسال إشعار إيقاف الري
  void notifyIrrigationStopped(IrrigationSystem system, double waterUsed) {
    sendNotification(IrrigationNotification(
      id: '${system.id}_irrigation_stopped_${DateTime.now().millisecondsSinceEpoch}',
      systemId: system.id,
      systemName: system.name,
      type: NotificationType.info,
      title: 'انتهاء الري',
      message: 'تم إيقاف الري في ${system.name}. تم استخدام ${waterUsed.toStringAsFixed(1)} لتر من المياه.',
      timestamp: DateTime.now(),
      isRead: false,
      actionRequired: false,
      priority: NotificationPriority.low,
    ));
  }

  /// إرسال إشعار خطأ في النظام
  void notifySystemError(IrrigationSystem system, String errorMessage) {
    sendNotification(IrrigationNotification(
      id: '${system.id}_error_${DateTime.now().millisecondsSinceEpoch}',
      systemId: system.id,
      systemName: system.name,
      type: NotificationType.error,
      title: 'خطأ في النظام',
      message: 'حدث خطأ في ${system.name}: $errorMessage',
      timestamp: DateTime.now(),
      isRead: false,
      actionRequired: true,
      priority: NotificationPriority.critical,
    ));
  }

  String _getTriggerName(IrrigationTrigger trigger) {
    switch (trigger) {
      case IrrigationTrigger.manual:
        return 'يدوي';
      case IrrigationTrigger.auto:
        return 'تلقائي';
      case IrrigationTrigger.smart:
        return 'ذكي';
      case IrrigationTrigger.scheduled:
        return 'مجدول';
    }
  }

  /// الحصول على لون الإشعار حسب النوع
  Color getNotificationColor(NotificationType type) {
    switch (type) {
      case NotificationType.info:
        return Colors.blue;
      case NotificationType.success:
        return Colors.green;
      case NotificationType.warning:
        return Colors.orange;
      case NotificationType.error:
        return Colors.red;
    }
  }

  /// الحصول على أيقونة الإشعار حسب النوع
  IconData getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.info:
        return Icons.info_outline;
      case NotificationType.success:
        return Icons.check_circle_outline;
      case NotificationType.warning:
        return Icons.warning_amber_outlined;
      case NotificationType.error:
        return Icons.error_outline;
    }
  }
}
