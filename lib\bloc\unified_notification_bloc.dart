import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:sam03/models/app_models.dart';
import 'package:sam03/services/unified_notification_service.dart';

// ===== الأحداث =====

abstract class UnifiedNotificationEvent extends Equatable {
  const UnifiedNotificationEvent();

  @override
  List<Object?> get props => [];
}

/// تحميل الإشعارات
class UnifiedNotificationEventLoad extends UnifiedNotificationEvent {}

/// إرسال إشعار جديد
class UnifiedNotificationEventSend extends UnifiedNotificationEvent {
  final UnifiedNotification notification;

  const UnifiedNotificationEventSend(this.notification);

  @override
  List<Object?> get props => [notification];
}

/// تحديد إشعار كمقروء
class UnifiedNotificationEventMarkAsRead extends UnifiedNotificationEvent {
  final String notificationId;

  const UnifiedNotificationEventMarkAsRead(this.notificationId);

  @override
  List<Object?> get props => [notificationId];
}

/// تحديد جميع الإشعارات كمقروءة
class UnifiedNotificationEventMarkAllAsRead extends UnifiedNotificationEvent {}

/// حذف إشعار
class UnifiedNotificationEventDelete extends UnifiedNotificationEvent {
  final String notificationId;

  const UnifiedNotificationEventDelete(this.notificationId);

  @override
  List<Object?> get props => [notificationId];
}

/// حذف جميع الإشعارات
class UnifiedNotificationEventClearAll extends UnifiedNotificationEvent {}

/// فلترة الإشعارات
class UnifiedNotificationEventFilter extends UnifiedNotificationEvent {
  final String filter;
  final NotificationCategory? category;

  const UnifiedNotificationEventFilter({
    required this.filter,
    this.category,
  });

  @override
  List<Object?> get props => [filter, category];
}

// ===== الحالات =====

abstract class UnifiedNotificationState extends Equatable {
  const UnifiedNotificationState();

  @override
  List<Object?> get props => [];
}

/// الحالة الأولية
class UnifiedNotificationStateInitial extends UnifiedNotificationState {}

/// حالة التحميل
class UnifiedNotificationStateLoading extends UnifiedNotificationState {}

/// حالة تحميل الإشعارات بنجاح
class UnifiedNotificationStateLoaded extends UnifiedNotificationState {
  final List<UnifiedNotification> notifications;
  final List<UnifiedNotification> filteredNotifications;
  final int unreadCount;
  final String currentFilter;
  final NotificationCategory? currentCategory;

  const UnifiedNotificationStateLoaded({
    required this.notifications,
    required this.filteredNotifications,
    required this.unreadCount,
    required this.currentFilter,
    this.currentCategory,
  });

  @override
  List<Object?> get props => [
        notifications,
        filteredNotifications,
        unreadCount,
        currentFilter,
        currentCategory,
      ];

  UnifiedNotificationStateLoaded copyWith({
    List<UnifiedNotification>? notifications,
    List<UnifiedNotification>? filteredNotifications,
    int? unreadCount,
    String? currentFilter,
    NotificationCategory? currentCategory,
  }) {
    return UnifiedNotificationStateLoaded(
      notifications: notifications ?? this.notifications,
      filteredNotifications: filteredNotifications ?? this.filteredNotifications,
      unreadCount: unreadCount ?? this.unreadCount,
      currentFilter: currentFilter ?? this.currentFilter,
      currentCategory: currentCategory ?? this.currentCategory,
    );
  }
}

/// حالة الخطأ
class UnifiedNotificationStateError extends UnifiedNotificationState {
  final String message;

  const UnifiedNotificationStateError(this.message);

  @override
  List<Object?> get props => [message];
}

/// حالة إرسال إشعار جديد
class UnifiedNotificationStateNotificationSent extends UnifiedNotificationState {
  final UnifiedNotification notification;

  const UnifiedNotificationStateNotificationSent(this.notification);

  @override
  List<Object?> get props => [notification];
}

// ===== BLoC =====

class UnifiedNotificationBloc extends Bloc<UnifiedNotificationEvent, UnifiedNotificationState> {
  final UnifiedNotificationService _notificationService;

  UnifiedNotificationBloc({
    UnifiedNotificationService? notificationService,
  })  : _notificationService = notificationService ?? UnifiedNotificationService(),
        super(UnifiedNotificationStateInitial()) {
    
    on<UnifiedNotificationEventLoad>(_onLoad);
    on<UnifiedNotificationEventSend>(_onSend);
    on<UnifiedNotificationEventMarkAsRead>(_onMarkAsRead);
    on<UnifiedNotificationEventMarkAllAsRead>(_onMarkAllAsRead);
    on<UnifiedNotificationEventDelete>(_onDelete);
    on<UnifiedNotificationEventClearAll>(_onClearAll);
    on<UnifiedNotificationEventFilter>(_onFilter);

    // الاستماع للإشعارات الجديدة
    _notificationService.addListener(_onNewNotification);
  }

  @override
  Future<void> close() {
    _notificationService.removeListener(_onNewNotification);
    return super.close();
  }

  void _onNewNotification(UnifiedNotification notification) {
    add(UnifiedNotificationEventLoad());
  }

  Future<void> _onLoad(
    UnifiedNotificationEventLoad event,
    Emitter<UnifiedNotificationState> emit,
  ) async {
    try {
      emit(UnifiedNotificationStateLoading());

      final notifications = _notificationService.notifications;
      final unreadCount = _notificationService.unreadCount;

      emit(UnifiedNotificationStateLoaded(
        notifications: notifications,
        filteredNotifications: notifications,
        unreadCount: unreadCount,
        currentFilter: 'all',
        currentCategory: null,
      ));
    } catch (e) {
      emit(UnifiedNotificationStateError('فشل في تحميل الإشعارات: $e'));
    }
  }

  Future<void> _onSend(
    UnifiedNotificationEventSend event,
    Emitter<UnifiedNotificationState> emit,
  ) async {
    try {
      _notificationService.sendNotification(event.notification);
      emit(UnifiedNotificationStateNotificationSent(event.notification));
      
      // إعادة تحميل الإشعارات
      add(UnifiedNotificationEventLoad());
    } catch (e) {
      emit(UnifiedNotificationStateError('فشل في إرسال الإشعار: $e'));
    }
  }

  Future<void> _onMarkAsRead(
    UnifiedNotificationEventMarkAsRead event,
    Emitter<UnifiedNotificationState> emit,
  ) async {
    try {
      _notificationService.markAsRead(event.notificationId);
      add(UnifiedNotificationEventLoad());
    } catch (e) {
      emit(UnifiedNotificationStateError('فشل في تحديث الإشعار: $e'));
    }
  }

  Future<void> _onMarkAllAsRead(
    UnifiedNotificationEventMarkAllAsRead event,
    Emitter<UnifiedNotificationState> emit,
  ) async {
    try {
      _notificationService.markAllAsRead();
      add(UnifiedNotificationEventLoad());
    } catch (e) {
      emit(UnifiedNotificationStateError('فشل في تحديث الإشعارات: $e'));
    }
  }

  Future<void> _onDelete(
    UnifiedNotificationEventDelete event,
    Emitter<UnifiedNotificationState> emit,
  ) async {
    try {
      _notificationService.deleteNotification(event.notificationId);
      add(UnifiedNotificationEventLoad());
    } catch (e) {
      emit(UnifiedNotificationStateError('فشل في حذف الإشعار: $e'));
    }
  }

  Future<void> _onClearAll(
    UnifiedNotificationEventClearAll event,
    Emitter<UnifiedNotificationState> emit,
  ) async {
    try {
      _notificationService.clearAllNotifications();
      add(UnifiedNotificationEventLoad());
    } catch (e) {
      emit(UnifiedNotificationStateError('فشل في حذف الإشعارات: $e'));
    }
  }

  Future<void> _onFilter(
    UnifiedNotificationEventFilter event,
    Emitter<UnifiedNotificationState> emit,
  ) async {
    try {
      final currentState = state;
      if (currentState is! UnifiedNotificationStateLoaded) return;

      var filteredNotifications = currentState.notifications;

      // فلترة حسب الحالة
      switch (event.filter) {
        case 'unread':
          filteredNotifications = filteredNotifications.where((n) => !n.isRead).toList();
          break;
        case 'critical':
          filteredNotifications = filteredNotifications.where((n) => 
              n.priority == NotificationPriority.critical ||
              n.priority == NotificationPriority.high
          ).toList();
          break;
        case 'all':
        default:
          // لا حاجة لفلترة
          break;
      }

      // فلترة حسب الفئة
      if (event.category != null) {
        filteredNotifications = filteredNotifications
            .where((n) => n.category == event.category)
            .toList();
      }

      emit(currentState.copyWith(
        filteredNotifications: filteredNotifications,
        currentFilter: event.filter,
        currentCategory: event.category,
      ));
    } catch (e) {
      emit(UnifiedNotificationStateError('فشل في فلترة الإشعارات: $e'));
    }
  }
}
