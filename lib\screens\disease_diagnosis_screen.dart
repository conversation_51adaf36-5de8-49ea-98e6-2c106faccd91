import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'dart:io';
import 'disease_treatment_screen.dart';
import 'package:sam03/widgets/unified_app_bar.dart';

class DiseaseDiagnosisScreen extends StatefulWidget {
  final File imageFile;
  final String plantType;

  const DiseaseDiagnosisScreen({
    super.key,
    required this.imageFile,
    required this.plantType,
  });

  @override
  State<DiseaseDiagnosisScreen> createState() => _DiseaseDiagnosisScreenState();
}

class _DiseaseDiagnosisScreenState extends State<DiseaseDiagnosisScreen> {
  bool _isAnalyzing = true;
  bool _hasResult = false;

  // بيانات محاكاة لنتائج التحليل
  final Map<String, dynamic> _analysisResults = {
    'طماطم': {
      'disease_name': 'اللفحة المتأخرة',
      'scientific_name': 'Phytophthora infestans',
      'confidence': 0.92,
      'description':
          'مرض فطري يصيب النباتات من عائلة الباذنجانيات مثل الطماطم والبطاطس. يظهر على شكل بقع بنية على الأوراق والسيقان والثمار.',
      'symptoms': [
        'بقع بنية داكنة على الأوراق',
        'تعفن الثمار',
        'ظهور نمو أبيض على السطح السفلي للأوراق في الظروف الرطبة',
        'تحول الأوراق إلى اللون البني ثم موتها',
      ],
      'conditions': [
        'الرطوبة العالية (أكثر من 90%)',
        'درجات الحرارة المعتدلة (15-25 درجة مئوية)',
        'الطقس الممطر أو الضبابي',
      ],
      'treatment': [
        'استخدام مبيدات فطرية نحاسية',
        'إزالة الأوراق المصابة وحرقها',
        'تجنب الري فوق الأوراق',
        'زيادة التهوية بين النباتات',
      ],
      'prevention': [
        'استخدام بذور مقاومة للمرض',
        'تناوب المحاصيل',
        'تجنب الزراعة في مناطق رطبة',
        'الري في الصباح الباكر',
      ],
    },
    'قمح': {
      'disease_name': 'صدأ الساق',
      'scientific_name': 'Puccinia graminis',
      'confidence': 0.88,
      'description':
          'مرض فطري يصيب نباتات القمح والشعير والشوفان، يظهر على شكل بثرات حمراء على الساق والأوراق.',
      'symptoms': [
        'بثرات بنية محمرة على الساق والأوراق',
        'تمزق أنسجة النبات',
        'ضعف عام في النبات',
        'انخفاض في المحصول',
      ],
      'conditions': [
        'درجات حرارة معتدلة (15-30 درجة مئوية)',
        'رطوبة عالية',
        'وجود العائل البديل (نبات البربريس)',
      ],
      'treatment': [
        'رش مبيدات فطرية',
        'إزالة النباتات المصابة',
        'تقليل كثافة الزراعة',
      ],
      'prevention': [
        'زراعة أصناف مقاومة',
        'إزالة العائل البديل',
        'الزراعة المبكرة',
        'تناوب المحاصيل',
      ],
    },
    'خيار': {
      'disease_name': 'البياض الدقيقي',
      'scientific_name': 'Podosphaera xanthii',
      'confidence': 0.95,
      'description':
          'مرض فطري يصيب العديد من النباتات بما فيها الخيار والكوسة. يظهر على شكل بقع بيضاء دقيقية على الأوراق.',
      'symptoms': [
        'بقع بيضاء دقيقية على الأوراق',
        'اصفرار الأوراق',
        'تشوه الأوراق',
        'ضعف النمو',
      ],
      'conditions': [
        'الطقس الجاف',
        'درجات حرارة معتدلة (20-27 درجة مئوية)',
        'الرطوبة المنخفضة',
      ],
      'treatment': [
        'رش مبيدات فطرية',
        'استخدام محلول بيكربونات الصوديوم',
        'إزالة الأوراق المصابة بشدة',
      ],
      'prevention': [
        'زراعة أصناف مقاومة',
        'تجنب الزراعة الكثيفة',
        'تحسين التهوية',
        'تجنب الإفراط في التسميد النيتروجيني',
      ],
    },
  };

  Map<String, dynamic> get _analysisResult =>
      _analysisResults[widget.plantType] ?? _analysisResults['طماطم'];

  @override
  void initState() {
    super.initState();
    _startAnalysis();
  }

  void _startAnalysis() {
    // محاكاة عملية التحليل
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _isAnalyzing = false;
          _hasResult = true;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم تشخيص المرض بنجاح')),
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: SimpleUnifiedAppBar(
        title: 'تشخيص ${widget.plantType}',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معاينة الصورة
            _buildImagePreview(),
            const SizedBox(height: 16),

            // حالة التحليل
            if (_isAnalyzing) _buildAnalyzingState(),

            // نتائج التحليل
            if (_hasResult) _buildResultsCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildImagePreview() {
    return Card(
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Stack(
            children: [
              Image.file(
                widget.imageFile,
                height: 200,
                width: double.infinity,
                fit: BoxFit.cover,
              ),
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.bottomCenter,
                      end: Alignment.topCenter,
                      colors: [
                        Colors.black.withOpacity(0.7),
                        Colors.transparent,
                      ],
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        _getPlantIcon(widget.plantType),
                        color: Colors.white,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        widget.plantType,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAnalyzingState() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            const Text(
              'جاري تحليل الصورة...',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              'يتم الآن تحليل الصورة باستخدام الذكاء الاصطناعي للكشف عن أمراض النبات',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'نتائج التحليل',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Chip(
                  label: Text(
                      '${(_analysisResult['confidence'] * 100).toInt()}% دقة'),
                  backgroundColor:
                      _getConfidenceColor(_analysisResult['confidence']),
                  labelStyle: const TextStyle(color: Colors.white),
                ),
              ],
            ),
            const Divider(),

            // معلومات المرض
            _buildInfoSection(
              title: 'اسم المرض',
              content: _analysisResult['disease_name'],
              icon: FontAwesomeIcons.disease,
              iconColor: Colors.red,
            ),

            _buildInfoSection(
              title: 'الاسم العلمي',
              content: _analysisResult['scientific_name'],
              icon: FontAwesomeIcons.microscope,
              iconColor: Colors.purple,
            ),

            _buildInfoSection(
              title: 'وصف المرض',
              content: _analysisResult['description'],
              icon: FontAwesomeIcons.circleInfo,
              iconColor: Colors.blue,
            ),

            // الأعراض
            _buildListSection(
              title: 'الأعراض',
              items: _analysisResult['symptoms'],
              icon: FontAwesomeIcons.notesMedical,
              iconColor: Colors.orange,
              itemIcon: Icons.arrow_right,
            ),

            // الظروف المساعدة
            _buildListSection(
              title: 'الظروف المساعدة',
              items: _analysisResult['conditions'],
              icon: FontAwesomeIcons.cloudSun,
              iconColor: Colors.amber,
              itemIcon: Icons.arrow_right,
            ),

            // أزرار الإجراءات
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    icon: const Icon(FontAwesomeIcons.kitMedical),
                    label: const Text('علاج النبات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => DiseaseTreatmentScreen(
                            plantType: widget.plantType,
                            diseaseName: _analysisResult['disease_name'],
                            treatments: _analysisResult['treatment'],
                            preventions: _analysisResult['prevention'],
                          ),
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: OutlinedButton.icon(
                    icon: const Icon(Icons.save_alt),
                    label: const Text('حفظ التشخيص'),
                    onPressed: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                            content: Text('تم حفظ التشخيص في السجل')),
                      );
                      Navigator.pop(context, {
                        'saved': true,
                        'plant': widget.plantType,
                        'disease': _analysisResult['disease_name'],
                        'confidence': _analysisResult['confidence'],
                      });
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoSection({
    required String title,
    required String content,
    required IconData icon,
    required Color iconColor,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 16, color: iconColor),
              const SizedBox(width: 8),
              Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Padding(
            padding: const EdgeInsets.only(right: 24.0),
            child: Text(content),
          ),
        ],
      ),
    );
  }

  Widget _buildListSection({
    required String title,
    required List<dynamic> items,
    required IconData icon,
    required Color iconColor,
    required IconData itemIcon,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 16, color: iconColor),
              const SizedBox(width: 8),
              Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ...items.map((item) => Padding(
                padding: const EdgeInsets.only(right: 24.0, bottom: 4.0),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(itemIcon, size: 16, color: iconColor),
                    const SizedBox(width: 8),
                    Expanded(child: Text(item)),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  IconData _getPlantIcon(String plantType) {
    switch (plantType) {
      case 'طماطم':
        return FontAwesomeIcons.apple;
      case 'قمح':
        return FontAwesomeIcons.wheatAwn;
      case 'خيار':
        return FontAwesomeIcons.seedling;
      default:
        return FontAwesomeIcons.leaf;
    }
  }

  Color _getConfidenceColor(double confidence) {
    if (confidence < 0.7) return Colors.red;
    if (confidence < 0.85) return Colors.orange;
    return Colors.green;
  }
}
