import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:sam03/bloc/irrigation_bloc.dart';
import 'package:sam03/models/app_models.dart';
import 'package:sam03/core/constants/app_constants.dart';
import 'package:sam03/widgets/unified_app_bar.dart';

/// شاشة إحصائيات ومراقبة نظام الري
class IrrigationStatisticsScreen extends StatefulWidget {
  final IrrigationSystem system;

  const IrrigationStatisticsScreen({
    super.key,
    required this.system,
  });

  @override
  State<IrrigationStatisticsScreen> createState() =>
      _IrrigationStatisticsScreenState();
}

class _IrrigationStatisticsScreenState extends State<IrrigationStatisticsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  DateTime _selectedStartDate =
      DateTime.now().subtract(const Duration(days: 7));
  DateTime _selectedEndDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadStatistics();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadStatistics() {
    context.read<IrrigationBloc>().add(
          IrrigationEventLoadStats(
            systemId: widget.system.id,
            startDate: _selectedStartDate,
            endDate: _selectedEndDate,
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('إحصائيات ${widget.system.name}'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          Stack(
            children: [
              IconButton(
                icon: const Icon(Icons.notifications),
                onPressed: () {
                  Navigator.pushNamed(context, '/notifications');
                },
              ),
              // يمكن إضافة عداد الإشعارات هنا
            ],
          ),
          IconButton(
            icon: const Icon(Icons.date_range),
            onPressed: _showDateRangePicker,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadStatistics,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(
              icon: Icon(FontAwesomeIcons.chartLine),
              text: 'الإحصائيات',
            ),
            Tab(
              icon: Icon(FontAwesomeIcons.droplet),
              text: 'استهلاك المياه',
            ),
            Tab(
              icon: Icon(FontAwesomeIcons.clockRotateLeft),
              text: 'السجلات',
            ),
          ],
        ),
      ),
      body: BlocConsumer<IrrigationBloc, IrrigationState>(
        listener: (context, state) {
          if (state is IrrigationStateError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is IrrigationStateLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          return TabBarView(
            controller: _tabController,
            children: [
              _buildStatisticsTab(state),
              _buildWaterUsageTab(state),
              _buildRecordsTab(state),
            ],
          );
        },
      ),
    );
  }

  Widget _buildStatisticsTab(IrrigationState state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // فترة الإحصائيات
          _buildDateRangeCard(),
          const SizedBox(height: 16),

          // إحصائيات سريعة
          _buildQuickStatsGrid(),
          const SizedBox(height: 16),

          // رسم بياني للرطوبة
          _buildMoistureChart(),
          const SizedBox(height: 16),

          // رسم بياني لدرجة الحرارة
          _buildTemperatureChart(),
          const SizedBox(height: 16),

          // تحليل الأداء
          _buildPerformanceAnalysis(),
        ],
      ),
    );
  }

  Widget _buildWaterUsageTab(IrrigationState state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // ملخص استهلاك المياه
          _buildWaterUsageSummary(),
          const SizedBox(height: 16),

          // رسم بياني لاستهلاك المياه
          _buildWaterUsageChart(),
          const SizedBox(height: 16),

          // مقارنة الاستهلاك
          _buildUsageComparison(),
          const SizedBox(height: 16),

          // توصيات توفير المياه
          _buildWaterSavingTips(),
        ],
      ),
    );
  }

  Widget _buildRecordsTab(IrrigationState state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // فلتر السجلات
          _buildRecordsFilter(),
          const SizedBox(height: 16),

          // قائمة السجلات
          _buildRecordsList(),
        ],
      ),
    );
  }

  Widget _buildDateRangeCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            const Icon(Icons.date_range, color: Colors.blue),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'فترة الإحصائيات',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${_formatDate(_selectedStartDate)} - ${_formatDate(_selectedEndDate)}',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            TextButton(
              onPressed: _showDateRangePicker,
              child: const Text('تغيير'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStatsGrid() {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 1.5,
      crossAxisSpacing: 12,
      mainAxisSpacing: 12,
      children: [
        _buildStatCard(
          title: 'إجمالي الري',
          value: '24',
          unit: 'مرة',
          icon: FontAwesomeIcons.droplet,
          color: Colors.blue,
        ),
        _buildStatCard(
          title: 'المياه المستهلكة',
          value: '1,250',
          unit: 'لتر',
          icon: FontAwesomeIcons.bucket,
          color: Colors.green,
        ),
        _buildStatCard(
          title: 'متوسط الرطوبة',
          value: '65',
          unit: '%',
          icon: FontAwesomeIcons.seedling,
          color: Colors.orange,
        ),
        _buildStatCard(
          title: 'كفاءة النظام',
          value: '92',
          unit: '%',
          icon: FontAwesomeIcons.chartLine,
          color: Colors.purple,
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required String unit,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.baseline,
              textBaseline: TextBaseline.alphabetic,
              children: [
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  unit,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMoistureChart() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تطور رطوبة التربة',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              height: 200,
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      FontAwesomeIcons.chartLine,
                      size: 48,
                      color: Colors.grey,
                    ),
                    SizedBox(height: 8),
                    Text(
                      'الرسم البياني قيد التطوير',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTemperatureChart() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تطور درجة الحرارة',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              height: 200,
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      FontAwesomeIcons.temperatureHalf,
                      size: 48,
                      color: Colors.grey,
                    ),
                    SizedBox(height: 8),
                    Text(
                      'الرسم البياني قيد التطوير',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceAnalysis() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تحليل الأداء',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildPerformanceItem(
              'كفاءة استهلاك المياه',
              '92%',
              Colors.green,
              'ممتاز',
            ),
            const Divider(),
            _buildPerformanceItem(
              'دقة التوقيت',
              '88%',
              Colors.orange,
              'جيد',
            ),
            const Divider(),
            _buildPerformanceItem(
              'استجابة الحساسات',
              '95%',
              Colors.green,
              'ممتاز',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceItem(
      String title, String value, Color color, String status) {
    return Row(
      children: [
        Expanded(
          child: Text(title),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            status,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildWaterUsageSummary() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ملخص استهلاك المياه',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildUsageItem('اليوم', '45 لتر', Colors.blue),
                ),
                Expanded(
                  child: _buildUsageItem('الأسبوع', '315 لتر', Colors.green),
                ),
                Expanded(
                  child: _buildUsageItem('الشهر', '1,250 لتر', Colors.orange),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUsageItem(String period, String amount, Color color) {
    return Column(
      children: [
        Text(
          period,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          amount,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildWaterUsageChart() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'استهلاك المياه اليومي',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              height: 200,
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      FontAwesomeIcons.chartBar,
                      size: 48,
                      color: Colors.grey,
                    ),
                    SizedBox(height: 8),
                    Text(
                      'الرسم البياني قيد التطوير',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUsageComparison() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'مقارنة الاستهلاك',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildComparisonItem(
                'مقارنة بالشهر الماضي', '-12%', Colors.green, true),
            const SizedBox(height: 8),
            _buildComparisonItem(
                'مقارنة بالمتوسط', '+5%', Colors.orange, false),
            const SizedBox(height: 8),
            _buildComparisonItem('الهدف الشهري', '78%', Colors.blue, true),
          ],
        ),
      ),
    );
  }

  Widget _buildComparisonItem(
      String title, String value, Color color, bool isGood) {
    return Row(
      children: [
        Icon(
          isGood ? Icons.trending_down : Icons.trending_up,
          color: color,
          size: 20,
        ),
        const SizedBox(width: 8),
        Expanded(child: Text(title)),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildWaterSavingTips() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'نصائح توفير المياه',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildTipItem('استخدم الري في الصباح الباكر لتقليل التبخر'),
            _buildTipItem('راقب رطوبة التربة بانتظام'),
            _buildTipItem('اضبط مدة الري حسب نوع النبات'),
            _buildTipItem('استفد من مياه الأمطار عند الإمكان'),
          ],
        ),
      ),
    );
  }

  Widget _buildTipItem(String tip) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(
            Icons.lightbulb_outline,
            color: Colors.amber,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              tip,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecordsFilter() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'فلتر السجلات',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'نوع الري',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'all', child: Text('الكل')),
                      DropdownMenuItem(value: 'manual', child: Text('يدوي')),
                      DropdownMenuItem(value: 'auto', child: Text('تلقائي')),
                      DropdownMenuItem(value: 'smart', child: Text('ذكي')),
                    ],
                    onChanged: (value) {},
                    value: 'all',
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'الفترة',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'week', child: Text('أسبوع')),
                      DropdownMenuItem(value: 'month', child: Text('شهر')),
                      DropdownMenuItem(value: 'custom', child: Text('مخصص')),
                    ],
                    onChanged: (value) {},
                    value: 'week',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecordsList() {
    // محاكاة بيانات السجلات
    final records = [
      {
        'time': '2024-01-15 08:30',
        'duration': '25 دقيقة',
        'water': '125 لتر',
        'type': 'تلقائي'
      },
      {
        'time': '2024-01-14 18:15',
        'duration': '30 دقيقة',
        'water': '150 لتر',
        'type': 'يدوي'
      },
      {
        'time': '2024-01-14 08:30',
        'duration': '20 دقيقة',
        'water': '100 لتر',
        'type': 'ذكي'
      },
      {
        'time': '2024-01-13 19:00',
        'duration': '35 دقيقة',
        'water': '175 لتر',
        'type': 'يدوي'
      },
    ];

    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.all(16.0),
            child: Text(
              'سجلات الري الأخيرة',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: records.length,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final record = records[index];
              return ListTile(
                leading: CircleAvatar(
                  backgroundColor:
                      _getTypeColor(record['type']!).withOpacity(0.1),
                  child: Icon(
                    _getTypeIcon(record['type']!),
                    color: _getTypeColor(record['type']!),
                    size: 20,
                  ),
                ),
                title: Text(record['time']!),
                subtitle: Text('${record['duration']} • ${record['water']}'),
                trailing: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getTypeColor(record['type']!).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    record['type']!,
                    style: TextStyle(
                      color: _getTypeColor(record['type']!),
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Color _getTypeColor(String type) {
    switch (type) {
      case 'يدوي':
        return Colors.blue;
      case 'تلقائي':
        return Colors.green;
      case 'ذكي':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  IconData _getTypeIcon(String type) {
    switch (type) {
      case 'يدوي':
        return Icons.touch_app;
      case 'تلقائي':
        return Icons.auto_mode;
      case 'ذكي':
        return FontAwesomeIcons.wandMagicSparkles;
      default:
        return Icons.water_drop;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showDateRangePicker() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(
        start: _selectedStartDate,
        end: _selectedEndDate,
      ),
    );

    if (picked != null) {
      setState(() {
        _selectedStartDate = picked.start;
        _selectedEndDate = picked.end;
      });
      _loadStatistics();
    }
  }
}
