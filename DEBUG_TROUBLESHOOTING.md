# دليل استكشاف أخطاء Flutter Debug

## خطأ: RemoteDebuggerExecutionContext: Timed out finding an execution context after 100 ms

### 🔍 **سبب المشكلة:**
هذا خطأ شائع في Flutter يحدث عندما:
- يكون هناك تعارض في عملية Debug
- مشاكل في الاتصال بين IDE والتطبيق
- تراكم في ذاكرة التطبيق
- مشاكل في Hot Reload

### 🛠️ **الحلول المتدرجة:**

#### **1. الحلول السريعة (جرب أولاً):**

```bash
# Hot Restart
Ctrl + Shift + F5
# أو في terminal
r
```

```bash
# إيقاف وإعادة تشغيل
flutter stop
flutter run
```

#### **2. تنظيف المشروع:**

```bash
# تنظيف شامل
flutter clean
flutter pub get
flutter run
```

#### **3. إعادة تشغيل IDE:**
- أغلق VS Code/Android Studio
- أعد فتحه
- شغل التطبيق مرة أخرى

#### **4. تنظيف ذاكرة التطبيق:**

```bash
# إيقاف جميع عمليات Flutter
flutter doctor --verbose
pkill -f flutter
pkill -f dart

# إعادة تشغيل
flutter run
```

#### **5. تحديث Flutter:**

```bash
# تحديث Flutter
flutter upgrade
flutter doctor

# إعادة تشغيل المشروع
flutter clean
flutter pub get
flutter run
```

#### **6. إعدادات VS Code المحسنة:**

تم إنشاء الملفات التالية لتحسين الأداء:
- `.vscode/launch.json` - إعدادات التشغيل
- `.vscode/settings.json` - إعدادات VS Code
- `analysis_options.yaml` - إعدادات التحليل

#### **7. تشغيل في وضع Profile:**

```bash
# تشغيل في وضع profile للأداء الأفضل
flutter run --profile
```

#### **8. تشغيل مع إعدادات محسنة:**

```bash
# تشغيل مع تعطيل بعض الميزات
flutter run --disable-service-auth-codes --disable-observatory-auth-codes
```

### 🔧 **إعدادات VS Code المحسنة:**

#### **launch.json:**
```json
{
    "name": "sam03",
    "request": "launch",
    "type": "dart",
    "program": "lib/main.dart",
    "args": [
        "--debug",
        "--enable-software-rendering"
    ],
    "vmAdditionalArgs": [
        "--disable-service-auth-codes",
        "--disable-observatory-auth-codes"
    ]
}
```

#### **settings.json:**
```json
{
    "dart.debugExternalPackageLibraries": false,
    "dart.debugSdkLibraries": false,
    "dart.evaluateGettersInDebugViews": false,
    "dart.hotReloadOnSave": "always"
}
```

### 📱 **حلول خاصة بالمنصة:**

#### **Android:**
```bash
# إعادة تشغيل ADB
adb kill-server
adb start-server

# تنظيف build Android
cd android
./gradlew clean
cd ..
flutter run
```

#### **iOS:**
```bash
# تنظيف build iOS
cd ios
rm -rf build/
cd ..
flutter run
```

### 🚀 **نصائح لتجنب المشكلة:**

#### **1. استخدم Hot Reload بدلاً من Hot Restart:**
```bash
# Hot Reload (أسرع)
Ctrl + S
# أو
r

# Hot Restart (عند الحاجة فقط)
Ctrl + Shift + F5
# أو
R
```

#### **2. تجنب التعديلات الكبيرة:**
- قم بتعديلات صغيرة متدرجة
- احفظ بانتظام
- استخدم Git للنسخ الاحتياطية

#### **3. مراقبة الذاكرة:**
- أغلق التطبيقات غير المستخدمة
- راقب استهلاك الذاكرة في IDE
- أعد تشغيل IDE عند الحاجة

#### **4. استخدم الأوامر المحسنة:**
```bash
# تشغيل مع verbose للتشخيص
flutter run --verbose

# تشغيل مع تتبع الأداء
flutter run --trace-startup
```

### 🔍 **تشخيص المشاكل:**

#### **فحص حالة Flutter:**
```bash
flutter doctor -v
flutter config
```

#### **فحص العمليات النشطة:**
```bash
# Windows
tasklist | findstr flutter
tasklist | findstr dart

# macOS/Linux
ps aux | grep flutter
ps aux | grep dart
```

#### **فحص المنافذ:**
```bash
# فحص المنافذ المستخدمة
netstat -an | findstr :8080
netstat -an | findstr :9000
```

### 📋 **قائمة تحقق سريعة:**

- [ ] جرب Hot Restart (Ctrl + Shift + F5)
- [ ] أعد تشغيل التطبيق (flutter stop && flutter run)
- [ ] نظف المشروع (flutter clean && flutter pub get)
- [ ] أعد تشغيل VS Code/IDE
- [ ] تحقق من flutter doctor
- [ ] جرب وضع profile (flutter run --profile)
- [ ] أعد تشغيل الجهاز/المحاكي

### 🆘 **إذا لم تنجح الحلول:**

#### **الحل الأخير:**
```bash
# حذف جميع ملفات build
flutter clean
rm -rf .dart_tool/
rm -rf build/
rm pubspec.lock

# إعادة تثبيت dependencies
flutter pub get

# إعادة تشغيل
flutter run
```

#### **إعادة إنشاء المشروع:**
```bash
# إنشاء مشروع جديد
flutter create new_project_name

# نسخ الملفات المهمة
cp -r lib/ new_project_name/
cp pubspec.yaml new_project_name/

# تشغيل المشروع الجديد
cd new_project_name
flutter pub get
flutter run
```

### 📞 **طلب المساعدة:**

إذا استمرت المشكلة، قدم المعلومات التالية:
- إصدار Flutter (`flutter --version`)
- نظام التشغيل
- IDE المستخدم
- رسالة الخطأ الكاملة
- الخطوات التي جربتها

### ✅ **الوقاية خير من العلاج:**

- احفظ عملك بانتظام
- استخدم Git للنسخ الاحتياطية
- أعد تشغيل IDE كل فترة
- راقب استهلاك الذاكرة
- حدث Flutter بانتظام
