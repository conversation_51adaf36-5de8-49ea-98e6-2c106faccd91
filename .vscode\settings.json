{"dart.debugExternalPackageLibraries": false, "dart.debugSdkLibraries": false, "dart.evaluateGettersInDebugViews": false, "dart.evaluateToStringInDebugViews": false, "dart.hotReloadOnSave": "always", "dart.previewFlutterUiGuides": true, "dart.previewFlutterUiGuidesCustomTracking": true, "dart.flutterHotReloadOnSave": "always", "dart.flutterCreateAndroidLanguage": "kotlin", "dart.flutterCreateIOSLanguage": "swift", "dart.flutterCreatePlatforms": ["android", "ios"], "dart.vmAdditionalArgs": ["--disable-service-auth-codes", "--disable-observatory-auth-codes"], "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/build": true, "**/.dart_tool": true, "**/.packages": true, "**/.pub": true}, "search.exclude": {"**/build": true, "**/.dart_tool": true, "**/.packages": true, "**/.pub": true}}