# نظام الإشعارات الموحد وشريط التطبيق الموحد

## نظرة عامة

تم تطوير نظام إشعارات موحد وشامل مع توحيد شريط التطبيق (AppBar) عبر كامل التطبيق لضمان تجربة مستخدم متسقة ومتماسكة.

## 🔧 المكونات الرئيسية

### 1. خدمة الإشعارات الموحدة (`UnifiedNotificationService`)

**الموقع:** `lib/services/unified_notification_service.dart`

**الميزات:**
- إدارة مركزية لجميع أنواع الإشعارات
- دعم فئات متعددة: الري، تشخيص الأمراض، السوق، النظام العام
- نظام أولويات (منخفضة، متوسطة، عالية، حرجة)
- إدارة حالة القراءة والإجراءات المطلوبة
- استمع للإشعارات في الوقت الفعلي

**الفئات المدعومة:**
```dart
enum NotificationCategory {
  irrigation,    // نظام الري
  disease,       // تشخيص الأمراض
  market,        // السوق الزراعي
  general,       // عام
  system,        // النظام
}
```

### 2. نموذج الإشعار الموحد (`UnifiedNotification`)

**الموقع:** `lib/models/app_models.dart`

**الخصائص:**
- معرف فريد
- فئة الإشعار
- نوع الإشعار (معلومات، نجاح، تحذير، خطأ)
- العنوان والرسالة
- الطابع الزمني
- حالة القراءة
- الأولوية
- البيانات الوصفية

### 3. شريط التطبيق الموحد (`UnifiedAppBar`)

**الموقع:** `lib/widgets/unified_app_bar.dart`

**الأنواع المتاحة:**

#### أ. شريط التطبيق الأساسي (`UnifiedAppBar`)
- عرض العنوان
- أيقونة الإشعارات مع العداد
- أزرار الإجراءات المخصصة
- زر الرجوع التلقائي

#### ب. شريط التطبيق للشاشة الرئيسية (`HomeUnifiedAppBar`)
- أيقونة التطبيق
- أزرار البحث والإعدادات
- الإشعارات مع العداد

#### ج. شريط التطبيق لنظام الري (`IrrigationUnifiedAppBar`)
- زر التحديث
- إجراءات مخصصة لنظام الري

#### د. شريط التطبيق لتشخيص الأمراض (`DiseaseUnifiedAppBar`)
- زر المساعدة
- إجراءات مخصصة للتشخيص

#### هـ. شريط التطبيق للسوق (`MarketUnifiedAppBar`)
- زر السلة مع العداد
- زر الفلترة

#### و. شريط التطبيق المبسط (`SimpleUnifiedAppBar`)
- للشاشات الفرعية البسيطة

### 4. شاشة الإشعارات الموحدة (`UnifiedNotificationsScreen`)

**الموقع:** `lib/screens/unified_notifications_screen.dart`

**الميزات:**
- عرض جميع الإشعارات
- فلترة حسب الحالة (الكل، غير مقروء، حرجة)
- فلترة حسب الفئة
- تحديد كمقروء
- حذف الإشعارات
- عرض تفاصيل الإشعار

### 5. BLoC الإشعارات الموحدة (`UnifiedNotificationBloc`)

**الموقع:** `lib/bloc/unified_notification_bloc.dart`

**الأحداث:**
- `UnifiedNotificationEventLoad` - تحميل الإشعارات
- `UnifiedNotificationEventSend` - إرسال إشعار جديد
- `UnifiedNotificationEventMarkAsRead` - تحديد كمقروء
- `UnifiedNotificationEventMarkAllAsRead` - تحديد الكل كمقروء
- `UnifiedNotificationEventDelete` - حذف إشعار
- `UnifiedNotificationEventClearAll` - حذف جميع الإشعارات
- `UnifiedNotificationEventFilter` - فلترة الإشعارات

**الحالات:**
- `UnifiedNotificationStateLoaded` - تحميل بنجاح
- `UnifiedNotificationStateError` - خطأ
- `UnifiedNotificationStateNotificationSent` - إرسال إشعار

## 🚀 التكامل مع الأجزاء الموجودة

### 1. نظام الري الذكي

**التحديثات:**
- استخدام `IrrigationUnifiedAppBar`
- إرسال إشعارات بدء وإيقاف الري
- إشعارات حالة الحساسات
- تكامل مع `IrrigationBloc`

**أنواع الإشعارات:**
- بدء الري
- إيقاف الري
- رطوبة التربة المنخفضة
- مستوى المياه المنخفض
- البطارية المنخفضة

### 2. تشخيص أمراض النباتات

**التحديثات:**
- استخدام `DiseaseUnifiedAppBar`
- إشعارات اكتمال التشخيص
- إشعارات فشل التشخيص

### 3. السوق الزراعي

**التحديثات:**
- استخدام `MarketUnifiedAppBar` للشاشة الرئيسية
- استخدام `SimpleUnifiedAppBar` لتفاصيل المنتج

### 4. الشاشة الرئيسية

**التحديثات:**
- استخدام `HomeUnifiedAppBar`
- عرض عداد الإشعارات
- أزرار البحث والإعدادات

## 📱 تجربة المستخدم

### 1. الاتساق البصري
- نفس التصميم والألوان عبر جميع الشاشات
- استخدام خط Tajawal الموحد
- موضع ثابت للعناصر

### 2. الإشعارات في الوقت الفعلي
- عداد الإشعارات غير المقروءة
- تحديث فوري عند وصول إشعارات جديدة
- ألوان وأيقونات مميزة لكل نوع

### 3. التنقل المحسن
- زر الرجوع التلقائي
- أزرار الإجراءات المناسبة لكل شاشة
- انتقال سلس بين الشاشات

## 🔧 كيفية الاستخدام

### 1. إرسال إشعار جديد

```dart
final notificationService = UnifiedNotificationService();

// إشعار نظام الري
notificationService.notifyIrrigationStarted(system, trigger);

// إشعار تشخيص الأمراض
notificationService.notifyDiagnosisCompleted(diagnosisId, diseaseName, confidence);

// إشعار عام
notificationService.notifyGeneral(
  title: 'عنوان الإشعار',
  message: 'محتوى الإشعار',
  type: NotificationType.info,
  priority: NotificationPriority.medium,
);
```

### 2. استخدام شريط التطبيق الموحد

```dart
// للشاشة الرئيسية
appBar: HomeUnifiedAppBar(
  title: 'الرئيسية',
  onSearchPressed: () => _handleSearch(),
  onSettingsPressed: () => _handleSettings(),
),

// لنظام الري
appBar: IrrigationUnifiedAppBar(
  title: 'الري الذكي',
  onRefreshPressed: () => _refreshData(),
),

// للشاشات البسيطة
appBar: SimpleUnifiedAppBar(
  title: 'تفاصيل المنتج',
),
```

### 3. الاستماع للإشعارات

```dart
class MyWidget extends StatefulWidget {
  @override
  void initState() {
    super.initState();
    UnifiedNotificationService().addListener(_onNotificationReceived);
  }

  void _onNotificationReceived(UnifiedNotification notification) {
    // التعامل مع الإشعار الجديد
    setState(() {
      // تحديث الواجهة
    });
  }

  @override
  void dispose() {
    UnifiedNotificationService().removeListener(_onNotificationReceived);
    super.dispose();
  }
}
```

## 🎯 الفوائد المحققة

### 1. الاتساق
- تجربة مستخدم موحدة عبر التطبيق
- تصميم متسق للشاشات
- نمط تفاعل موحد

### 2. الكفاءة
- إدارة مركزية للإشعارات
- تقليل تكرار الكود
- سهولة الصيانة والتطوير

### 3. المرونة
- سهولة إضافة أنواع إشعارات جديدة
- تخصيص شريط التطبيق لكل شاشة
- نظام أولويات مرن

### 4. الأداء
- تحديثات فعالة للإشعارات
- إدارة ذاكرة محسنة
- تحميل سريع للواجهات

## 🔮 التطوير المستقبلي

### 1. الميزات المقترحة
- إشعارات push خارجية
- تخصيص أصوات الإشعارات
- جدولة الإشعارات
- إحصائيات الإشعارات

### 2. التحسينات
- دعم الثيمات المتعددة
- تخصيص ألوان الإشعارات
- فلترة متقدمة
- بحث في الإشعارات

### 3. التكامل
- ربط مع قواعد البيانات الخارجية
- مزامنة عبر الأجهزة
- تصدير الإشعارات
- تحليلات الاستخدام

## 📋 قائمة التحقق للمطورين

### ✅ تم الإنجاز
- [x] خدمة الإشعارات الموحدة
- [x] نماذج البيانات الموحدة
- [x] شريط التطبيق الموحد
- [x] شاشة الإشعارات الموحدة
- [x] BLoC الإشعارات
- [x] تكامل مع نظام الري
- [x] تكامل مع تشخيص الأمراض
- [x] تكامل مع السوق الزراعي
- [x] تحديث الشاشة الرئيسية

### 🔄 قيد التطوير
- [ ] إشعارات push خارجية
- [ ] تخصيص الثيمات
- [ ] إحصائيات الإشعارات

### 📝 ملاحظات للمطورين
1. استخدم دائماً `UnifiedAppBar` أو أحد أنواعه المخصصة
2. أرسل الإشعارات عبر `UnifiedNotificationService`
3. اتبع نمط الألوان والخطوط الموحد
4. اختبر التكامل مع جميع أجزاء التطبيق

## 🎉 الخلاصة

تم تطوير نظام إشعارات موحد وشامل مع شريط تطبيق موحد يضمن:
- تجربة مستخدم متسقة ومتماسكة
- إدارة فعالة للإشعارات عبر جميع أجزاء التطبيق
- سهولة الصيانة والتطوير المستقبلي
- أداء محسن وتفاعل سلس

النظام جاهز للاستخدام ويمكن توسيعه بسهولة لإضافة ميزات جديدة في المستقبل.
