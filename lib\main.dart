import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:sam03/bloc/auth_bloc.dart';
import 'package:sam03/bloc/market_bloc.dart';
import 'package:sam03/bloc/theme_bloc.dart';
import 'package:sam03/bloc/irrigation_bloc.dart';
import 'package:sam03/bloc/disease_diagnosis_bloc.dart';
import 'package:sam03/bloc/unified_notification_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import 'firebase_options.dart';
import 'repositories/auth_repository.dart';
import 'repositories/market_repository.dart';
import 'repositories/irrigation_repository.dart';
import 'repositories/disease_diagnosis_repository.dart';
import 'services/database_service.dart';
import 'screens/auth_screens.dart';
import 'screens/home_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // تهيئة قاعدة البيانات
  try {
    await DatabaseService().initializeDatabase();
  } catch (e) {
    print('تحذير: فشل في تهيئة قاعدة البيانات: $e');
  }

  final prefs = await SharedPreferences.getInstance();
  final bool isLoggedIn = prefs.getBool('isLoggedIn') ?? false;
  final String? userId = prefs.getString('userId');
  final bool isDarkMode = prefs.getBool('isDarkMode') ?? false;

  runApp(
    MultiBlocProvider(
      providers: [
        BlocProvider<AuthBloc>(
          create: (context) => AuthBloc(
            authRepository: AuthRepository(),
            prefs: prefs,
          )..add(isLoggedIn && userId != null
              ? AuthEventCheckSession(userId)
              : AuthEventInitial()),
        ),
        BlocProvider<ThemeBloc>(
          create: (context) => ThemeBloc(
            prefs: prefs,
            initialIsDark: isDarkMode,
          ),
        ),
        BlocProvider<MarketBloc>(
          create: (context) => MarketBloc(marketRepository: MarketRepository())
            ..add(MarketEventLoadProducts()),
        ),
        BlocProvider<IrrigationBloc>(
          create: (context) => IrrigationBloc(
            irrigationRepository: IrrigationRepository(),
          ),
        ),
        BlocProvider<DiseaseDiagnosisBloc>(
          create: (context) => DiseaseDiagnosisBloc(
            repository: DiseaseDiagnosisRepository(),
          ),
        ),
        BlocProvider<UnifiedNotificationBloc>(
          create: (context) =>
              UnifiedNotificationBloc()..add(UnifiedNotificationEventLoad()),
        ),
      ],
      child: const AgriculturalMarketApp(),
    ),
  );
}

class AgriculturalMarketApp extends StatelessWidget {
  const AgriculturalMarketApp({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return MaterialApp(
          title: 'SAM - مساعدك الزراعي الذكي',
          debugShowCheckedModeBanner: false,
          theme: ThemeData(
            primaryColor: const Color(0xFF2E7D32),
            colorScheme: const ColorScheme.light(
              primary: Color(0xFF2E7D32),
              primaryContainer: Color(0xFF4CAF50),
              secondary: Color(0xFF795548),
              surface: Colors.white,
              error: Color(0xFFD32F2F),
              onPrimary: Colors.white,
              onSecondary: Colors.white,
              onSurface: Color(0xFF212121),
              onError: Colors.white,
            ),
            fontFamily: 'Tajawal',
            appBarTheme: const AppBarTheme(
              backgroundColor: Colors.white,
              foregroundColor: Color(0xFF212121),
              elevation: 1,
              centerTitle: true,
            ),
            cardTheme: CardTheme(
              elevation: 2,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
            ),
          ),
          darkTheme: ThemeData(
            primaryColor: const Color(0xFF4CAF50),
            colorScheme: const ColorScheme.dark(
              primary: Color(0xFF4CAF50),
              primaryContainer: Color(0xFF2E7D32),
              secondary: Color(0xFF795548),
              surface: Color(0xFF1E1E1E),
              error: Color(0xFFD32F2F),
              onPrimary: Colors.white,
              onSecondary: Colors.white,
              onSurface: Color(0xFFFAFAFA),
              onError: Colors.white,
            ),
            fontFamily: 'Tajawal',
            appBarTheme: const AppBarTheme(
              backgroundColor: Color(0xFF1E1E1E),
              foregroundColor: Color(0xFFFAFAFA),
              elevation: 1,
              centerTitle: true,
            ),
            cardTheme: CardTheme(
              color: const Color(0xFF1E1E1E),
              elevation: 2,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
            ),
          ),
          themeMode: themeState.isDark ? ThemeMode.dark : ThemeMode.light,
          locale: const Locale('ar'),
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('ar', ''),
          ],
          home: BlocBuilder<AuthBloc, AuthState>(
            builder: (context, state) {
              if (state is AuthStateAuthenticated) {
                return const HomePage();
              } else {
                return const LoginScreen();
              }
            },
          ),
        );
      },
    );
  }
}
