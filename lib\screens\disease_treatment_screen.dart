import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:sam03/widgets/unified_app_bar.dart';

class DiseaseTreatmentScreen extends StatefulWidget {
  final String plantType;
  final String diseaseName;
  final List<dynamic> treatments;
  final List<dynamic> preventions;

  const DiseaseTreatmentScreen({
    super.key,
    required this.plantType,
    required this.diseaseName,
    required this.treatments,
    required this.preventions,
  });

  @override
  State<DiseaseTreatmentScreen> createState() => _DiseaseTreatmentScreenState();
}

class _DiseaseTreatmentScreenState extends State<DiseaseTreatmentScreen> {
  late List<bool> _treatmentCompleted;
  bool _allCompleted = false;

  // بيانات محاكاة للمبيدات المقترحة
  final Map<String, List<Map<String, dynamic>>> _suggestedPesticides = {
    'اللفحة المتأخرة': [
      {
        'name': 'مانكوزيب',
        'type': 'مبيد فطري وقائي',
        'dosage': '2.5 جم/لتر ماء',
        'frequency': 'كل 7-10 أيام',
        'notes': 'يستخدم قبل ظهور المرض أو عند بداية ظهوره',
      },
      {
        'name': 'كلوروثالونيل',
        'type': 'مبيد فطري وقائي',
        'dosage': '2 مل/لتر ماء',
        'frequency': 'كل 7-14 يوم',
        'notes': 'فعال ضد مجموعة واسعة من الأمراض الفطرية',
      },
      {
        'name': 'أوكسي كلوريد النحاس',
        'type': 'مبيد فطري نحاسي',
        'dosage': '3 جم/لتر ماء',
        'frequency': 'كل 7-10 أيام',
        'notes': 'يفضل استخدامه في الطقس الرطب',
      },
    ],
    'البياض الدقيقي': [
      {
        'name': 'كبريت ميكروني',
        'type': 'مبيد فطري',
        'dosage': '2.5 جم/لتر ماء',
        'frequency': 'كل 7-10 أيام',
        'notes': 'لا يستخدم في درجات الحرارة المرتفعة (فوق 30 درجة مئوية)',
      },
      {
        'name': 'تريفلوكسيستروبين',
        'type': 'مبيد فطري جهازي',
        'dosage': '0.5 مل/لتر ماء',
        'frequency': 'كل 10-14 يوم',
        'notes': 'فعال جدًا ضد البياض الدقيقي',
      },
    ],
    'صدأ الساق': [
      {
        'name': 'تيبوكونازول',
        'type': 'مبيد فطري جهازي',
        'dosage': '1 مل/لتر ماء',
        'frequency': 'كل 14-21 يوم',
        'notes': 'فعال ضد أمراض الصدأ',
      },
      {
        'name': 'بروبيكونازول',
        'type': 'مبيد فطري جهازي',
        'dosage': '1 مل/لتر ماء',
        'frequency': 'كل 14 يوم',
        'notes': 'يستخدم عند ظهور أعراض المرض',
      },
    ],
  };

  List<Map<String, dynamic>> get _pesticides {
    // التحقق من وجود المرض في قائمة المبيدات المقترحة
    if (_suggestedPesticides.containsKey(widget.diseaseName)) {
      return _suggestedPesticides[widget.diseaseName]!;
    }

    // إذا لم يكن موجودًا، نحاول استخدام اللفحة المتأخرة كقيمة افتراضية
    if (_suggestedPesticides.containsKey('اللفحة المتأخرة')) {
      return _suggestedPesticides['اللفحة المتأخرة']!;
    }

    // إذا لم تكن اللفحة المتأخرة موجودة أيضًا، نعيد قائمة فارغة
    return [];
  }

  @override
  void initState() {
    super.initState();
    _treatmentCompleted =
        List.generate(widget.treatments.length, (index) => false);
  }

  void _checkAllCompleted() {
    setState(() {
      _allCompleted = !_treatmentCompleted.contains(false);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: SimpleUnifiedAppBar(
        title: 'علاج ${widget.diseaseName}',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // بطاقة معلومات المرض
            _buildDiseaseInfoCard(),
            const SizedBox(height: 16),

            // بطاقة المبيدات المقترحة
            _buildPesticidesCard(),
            const SizedBox(height: 16),

            // بطاقة خطوات العلاج
            _buildTreatmentStepsCard(),
            const SizedBox(height: 16),

            // بطاقة الوقاية
            _buildPreventionCard(),
            const SizedBox(height: 24),

            // زر تأكيد العلاج
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                icon: const Icon(Icons.check_circle),
                label: const Text('تم علاج النبات'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _allCompleted
                      ? Theme.of(context).primaryColor
                      : Colors.grey,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                onPressed: _allCompleted
                    ? () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                              content: Text('تم تسجيل علاج النبات بنجاح')),
                        );
                        Navigator.pop(context, {'treated': true});
                      }
                    : null,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDiseaseInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _getPlantIcon(widget.plantType),
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  widget.plantType,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(width: 16),
                const Icon(
                  FontAwesomeIcons.disease,
                  color: Colors.red,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    widget.diseaseName,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
              ],
            ),
            const Divider(),
            const Text(
              'يرجى اتباع الخطوات التالية لعلاج النبات والوقاية من انتشار المرض',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPesticidesCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  FontAwesomeIcons.sprayCan,
                  color: Colors.orange,
                  size: 16,
                ),
                const SizedBox(width: 8),
                const Text(
                  'المبيدات المقترحة',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const Spacer(),
                Text(
                  '${_pesticides.length} مبيدات',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._pesticides.map((pesticide) => _buildPesticideItem(pesticide)),
          ],
        ),
      ),
    );
  }

  Widget _buildPesticideItem(Map<String, dynamic> pesticide) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.orange.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.orange.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                FontAwesomeIcons.flask,
                color: Colors.orange,
                size: 14,
              ),
              const SizedBox(width: 8),
              Text(
                pesticide['name'],
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  pesticide['type'],
                  style: const TextStyle(
                    fontSize: 10,
                    color: Colors.orange,
                  ),
                ),
              ),
            ],
          ),
          const Divider(),
          _buildPesticideInfoRow(
            icon: Icons.science,
            label: 'الجرعة:',
            value: pesticide['dosage'],
          ),
          _buildPesticideInfoRow(
            icon: Icons.calendar_today,
            label: 'التكرار:',
            value: pesticide['frequency'],
          ),
          if (pesticide['notes'] != null)
            _buildPesticideInfoRow(
              icon: Icons.info_outline,
              label: 'ملاحظات:',
              value: pesticide['notes'],
            ),
        ],
      ),
    );
  }

  Widget _buildPesticideInfoRow({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4.0),
      child: Row(
        children: [
          Icon(
            icon,
            size: 14,
            color: Colors.grey.shade600,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTreatmentStepsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  FontAwesomeIcons.listCheck,
                  color: Colors.green,
                  size: 16,
                ),
                const SizedBox(width: 8),
                const Text(
                  'خطوات العلاج',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const Spacer(),
                Text(
                  '${_treatmentCompleted.where((completed) => completed).length}/${widget.treatments.length} مكتمل',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...List.generate(
              widget.treatments.length,
              (index) => _buildTreatmentStep(
                step: widget.treatments[index],
                index: index,
                isCompleted: _treatmentCompleted[index],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTreatmentStep({
    required String step,
    required int index,
    required bool isCompleted,
  }) {
    return CheckboxListTile(
      value: isCompleted,
      onChanged: (value) {
        setState(() {
          _treatmentCompleted[index] = value ?? false;
          _checkAllCompleted();
        });
      },
      title: Text(
        step,
        style: TextStyle(
          decoration: isCompleted ? TextDecoration.lineThrough : null,
          color: isCompleted ? Colors.grey : null,
        ),
      ),
      secondary: CircleAvatar(
        backgroundColor: isCompleted ? Colors.green : Colors.grey.shade200,
        child: Text(
          '${index + 1}',
          style: TextStyle(
            color: isCompleted ? Colors.white : Colors.grey.shade700,
          ),
        ),
      ),
      activeColor: Colors.green,
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildPreventionCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  FontAwesomeIcons.shieldVirus,
                  color: Colors.blue,
                  size: 16,
                ),
                const SizedBox(width: 8),
                const Text(
                  'الوقاية المستقبلية',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...widget.preventions.map((prevention) => Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Icon(
                        Icons.shield,
                        size: 16,
                        color: Colors.blue,
                      ),
                      const SizedBox(width: 8),
                      Expanded(child: Text(prevention)),
                    ],
                  ),
                )),
          ],
        ),
      ),
    );
  }

  IconData _getPlantIcon(String plantType) {
    switch (plantType) {
      case 'طماطم':
        return FontAwesomeIcons.apple;
      case 'قمح':
        return FontAwesomeIcons.wheatAwn;
      case 'خيار':
        return FontAwesomeIcons.seedling;
      default:
        return FontAwesomeIcons.leaf;
    }
  }
}
