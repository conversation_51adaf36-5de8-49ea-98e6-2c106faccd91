import 'package:flutter/material.dart';
import 'package:sam03/models/app_models.dart';
import 'package:sam03/core/constants/app_constants.dart';

/// خدمة الإشعارات الموحدة لجميع أجزاء التطبيق
class UnifiedNotificationService {
  static final UnifiedNotificationService _instance = UnifiedNotificationService._internal();
  factory UnifiedNotificationService() => _instance;
  UnifiedNotificationService._internal();

  final List<UnifiedNotification> _notifications = [];
  final List<Function(UnifiedNotification)> _listeners = [];

  /// إضافة مستمع للإشعارات
  void addListener(Function(UnifiedNotification) listener) {
    _listeners.add(listener);
  }

  /// إزالة مستمع للإشعارات
  void removeListener(Function(UnifiedNotification) listener) {
    _listeners.remove(listener);
  }

  /// إرسال إشعار جديد
  void sendNotification(UnifiedNotification notification) {
    _notifications.insert(0, notification);
    
    // الاحتفاظ بآخر 100 إشعار فقط
    if (_notifications.length > 100) {
      _notifications.removeRange(100, _notifications.length);
    }

    // إشعار جميع المستمعين
    for (final listener in _listeners) {
      listener(notification);
    }
  }

  /// الحصول على جميع الإشعارات
  List<UnifiedNotification> get notifications => List.unmodifiable(_notifications);

  /// الحصول على الإشعارات غير المقروءة
  List<UnifiedNotification> get unreadNotifications =>
      _notifications.where((n) => !n.isRead).toList();

  /// عدد الإشعارات غير المقروءة
  int get unreadCount => unreadNotifications.length;

  /// تحديد إشعار كمقروء
  void markAsRead(String notificationId) {
    final index = _notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      _notifications[index] = _notifications[index].copyWith(isRead: true);
    }
  }

  /// تحديد جميع الإشعارات كمقروءة
  void markAllAsRead() {
    for (int i = 0; i < _notifications.length; i++) {
      _notifications[i] = _notifications[i].copyWith(isRead: true);
    }
  }

  /// حذف إشعار
  void deleteNotification(String notificationId) {
    _notifications.removeWhere((n) => n.id == notificationId);
  }

  /// حذف جميع الإشعارات
  void clearAllNotifications() {
    _notifications.clear();
  }

  /// فلترة الإشعارات حسب النوع
  List<UnifiedNotification> getNotificationsByType(NotificationCategory category) {
    return _notifications.where((n) => n.category == category).toList();
  }

  /// فلترة الإشعارات حسب الأولوية
  List<UnifiedNotification> getNotificationsByPriority(NotificationPriority priority) {
    return _notifications.where((n) => n.priority == priority).toList();
  }

  /// الحصول على الإشعارات الحرجة
  List<UnifiedNotification> get criticalNotifications =>
      _notifications.where((n) => 
          n.priority == NotificationPriority.critical ||
          n.priority == NotificationPriority.high
      ).toList();

  // ===== إشعارات نظام الري =====

  /// إشعار بدء الري
  void notifyIrrigationStarted(IrrigationSystem system, IrrigationTrigger trigger) {
    final triggerName = _getTriggerName(trigger);
    sendNotification(UnifiedNotification(
      id: '${system.id}_irrigation_started_${DateTime.now().millisecondsSinceEpoch}',
      category: NotificationCategory.irrigation,
      type: NotificationType.success,
      title: 'بدء الري',
      message: 'تم بدء الري في ${system.name} ($triggerName)',
      timestamp: DateTime.now(),
      isRead: false,
      actionRequired: false,
      priority: NotificationPriority.medium,
      relatedId: system.id,
      metadata: {
        'systemName': system.name,
        'trigger': trigger.toString(),
      },
    ));
  }

  /// إشعار إيقاف الري
  void notifyIrrigationStopped(IrrigationSystem system, double waterUsed) {
    sendNotification(UnifiedNotification(
      id: '${system.id}_irrigation_stopped_${DateTime.now().millisecondsSinceEpoch}',
      category: NotificationCategory.irrigation,
      type: NotificationType.info,
      title: 'انتهاء الري',
      message: 'تم إيقاف الري في ${system.name}. تم استخدام ${waterUsed.toStringAsFixed(1)} لتر من المياه.',
      timestamp: DateTime.now(),
      isRead: false,
      actionRequired: false,
      priority: NotificationPriority.low,
      relatedId: system.id,
      metadata: {
        'systemName': system.name,
        'waterUsed': waterUsed,
      },
    ));
  }

  /// إشعار رطوبة التربة المنخفضة
  void notifyLowSoilMoisture(IrrigationSystem system) {
    final moisture = system.sensors.soilMoisture;
    sendNotification(UnifiedNotification(
      id: '${system.id}_moisture_low_${DateTime.now().millisecondsSinceEpoch}',
      category: NotificationCategory.irrigation,
      type: NotificationType.warning,
      title: 'رطوبة التربة منخفضة',
      message: 'رطوبة التربة في ${system.name} منخفضة (${(moisture * 100).toInt()}%). يُنصح بالري.',
      timestamp: DateTime.now(),
      isRead: false,
      actionRequired: true,
      priority: NotificationPriority.high,
      relatedId: system.id,
      metadata: {
        'systemName': system.name,
        'moistureLevel': moisture,
      },
    ));
  }

  /// إشعار مستوى المياه المنخفض
  void notifyLowWaterLevel(IrrigationSystem system) {
    final waterLevel = system.sensors.waterLevel;
    sendNotification(UnifiedNotification(
      id: '${system.id}_water_low_${DateTime.now().millisecondsSinceEpoch}',
      category: NotificationCategory.irrigation,
      type: NotificationType.error,
      title: 'مستوى المياه منخفض',
      message: 'مستوى المياه في ${system.name} منخفض جداً (${(waterLevel * 100).toInt()}%). يرجى إعادة التعبئة.',
      timestamp: DateTime.now(),
      isRead: false,
      actionRequired: true,
      priority: NotificationPriority.critical,
      relatedId: system.id,
      metadata: {
        'systemName': system.name,
        'waterLevel': waterLevel,
      },
    ));
  }

  /// إشعار البطارية المنخفضة
  void notifyLowBattery(IrrigationSystem system) {
    final batteryLevel = system.sensors.batteryLevel;
    sendNotification(UnifiedNotification(
      id: '${system.id}_battery_low_${DateTime.now().millisecondsSinceEpoch}',
      category: NotificationCategory.irrigation,
      type: NotificationType.warning,
      title: 'البطارية منخفضة',
      message: 'مستوى البطارية في ${system.name} منخفض (${(batteryLevel * 100).toInt()}%). يرجى الشحن أو الاستبدال.',
      timestamp: DateTime.now(),
      isRead: false,
      actionRequired: true,
      priority: NotificationPriority.high,
      relatedId: system.id,
      metadata: {
        'systemName': system.name,
        'batteryLevel': batteryLevel,
      },
    ));
  }

  // ===== إشعارات تشخيص الأمراض =====

  /// إشعار اكتمال التشخيص
  void notifyDiagnosisCompleted(String diagnosisId, String diseaseName, double confidence) {
    sendNotification(UnifiedNotification(
      id: 'diagnosis_completed_${DateTime.now().millisecondsSinceEpoch}',
      category: NotificationCategory.disease,
      type: NotificationType.success,
      title: 'اكتمل التشخيص',
      message: 'تم تشخيص المرض: $diseaseName بدقة ${(confidence * 100).toInt()}%',
      timestamp: DateTime.now(),
      isRead: false,
      actionRequired: true,
      priority: NotificationPriority.medium,
      relatedId: diagnosisId,
      metadata: {
        'diseaseName': diseaseName,
        'confidence': confidence,
      },
    ));
  }

  /// إشعار فشل التشخيص
  void notifyDiagnosisFailed(String error) {
    sendNotification(UnifiedNotification(
      id: 'diagnosis_failed_${DateTime.now().millisecondsSinceEpoch}',
      category: NotificationCategory.disease,
      type: NotificationType.error,
      title: 'فشل في التشخيص',
      message: 'لم يتم التمكن من تشخيص المرض: $error',
      timestamp: DateTime.now(),
      isRead: false,
      actionRequired: false,
      priority: NotificationPriority.medium,
      relatedId: null,
      metadata: {
        'error': error,
      },
    ));
  }

  // ===== إشعارات النظام العام =====

  /// إشعار عام
  void notifyGeneral({
    required String title,
    required String message,
    NotificationType type = NotificationType.info,
    NotificationPriority priority = NotificationPriority.medium,
    bool actionRequired = false,
    String? relatedId,
    Map<String, dynamic>? metadata,
  }) {
    sendNotification(UnifiedNotification(
      id: 'general_${DateTime.now().millisecondsSinceEpoch}',
      category: NotificationCategory.general,
      type: type,
      title: title,
      message: message,
      timestamp: DateTime.now(),
      isRead: false,
      actionRequired: actionRequired,
      priority: priority,
      relatedId: relatedId,
      metadata: metadata,
    ));
  }

  /// إشعار تحديث التطبيق
  void notifyAppUpdate(String version, String features) {
    sendNotification(UnifiedNotification(
      id: 'app_update_${DateTime.now().millisecondsSinceEpoch}',
      category: NotificationCategory.system,
      type: NotificationType.info,
      title: 'تحديث جديد متاح',
      message: 'الإصدار $version متاح الآن مع ميزات جديدة: $features',
      timestamp: DateTime.now(),
      isRead: false,
      actionRequired: true,
      priority: NotificationPriority.medium,
      relatedId: null,
      metadata: {
        'version': version,
        'features': features,
      },
    ));
  }

  // ===== دوال مساعدة =====

  String _getTriggerName(IrrigationTrigger trigger) {
    switch (trigger) {
      case IrrigationTrigger.manual:
        return 'يدوي';
      case IrrigationTrigger.auto:
        return 'تلقائي';
      case IrrigationTrigger.smart:
        return 'ذكي';
      case IrrigationTrigger.scheduled:
        return 'مجدول';
    }
  }

  /// الحصول على لون الإشعار حسب النوع
  Color getNotificationColor(NotificationType type) {
    switch (type) {
      case NotificationType.info:
        return Colors.blue;
      case NotificationType.success:
        return Colors.green;
      case NotificationType.warning:
        return Colors.orange;
      case NotificationType.error:
        return Colors.red;
    }
  }

  /// الحصول على أيقونة الإشعار حسب النوع
  IconData getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.info:
        return Icons.info_outline;
      case NotificationType.success:
        return Icons.check_circle_outline;
      case NotificationType.warning:
        return Icons.warning_amber_outlined;
      case NotificationType.error:
        return Icons.error_outline;
    }
  }

  /// الحصول على أيقونة الفئة
  IconData getCategoryIcon(NotificationCategory category) {
    switch (category) {
      case NotificationCategory.irrigation:
        return Icons.water_drop;
      case NotificationCategory.disease:
        return Icons.local_hospital;
      case NotificationCategory.market:
        return Icons.store;
      case NotificationCategory.general:
        return Icons.notifications;
      case NotificationCategory.system:
        return Icons.settings;
    }
  }
}
