import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:math' show min;
import 'package:intl/intl.dart';
import 'package:sam03/bloc/auth_bloc.dart';
import 'package:sam03/bloc/market_bloc.dart';

import '../models/app_models.dart';
import '../widgets/product_card.dart';
import 'edit_product_screen.dart';
import 'package:sam03/widgets/unified_app_bar.dart';

class MarketplaceScreen extends StatefulWidget {
  const MarketplaceScreen({super.key});

  @override
  State<MarketplaceScreen> createState() => _MarketplaceScreenState();
}

class _MarketplaceScreenState extends State<MarketplaceScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        context.read<MarketBloc>().add(MarketEventLoadProducts());
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return const ProductsTab();
  }
}

class ProductsTab extends StatelessWidget {
  const ProductsTab({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<MarketBloc, MarketState>(
      listener: (context, state) {
        // عرض رسالة عند إضافة أو حذف منتج
        if (state is MarketStateProductAdded) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم إضافة المنتج بنجاح')),
          );
          // إعادة تحميل قائمة المنتجات
          context.read<MarketBloc>().add(MarketEventLoadProducts());
        } else if (state is MarketStateProductDeleted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم حذف المنتج بنجاح')),
          );
          // إعادة تحميل قائمة المنتجات
          context.read<MarketBloc>().add(MarketEventLoadProducts());
        } else if (state is MarketStateProductUpdated) {
          // إعادة تحميل قائمة المنتجات
          context.read<MarketBloc>().add(MarketEventLoadProducts());
        }
      },
      builder: (context, state) {
        // إذا كانت الحالة الأولية، قم بتحميل المنتجات
        if (state is MarketStateInitial) {
          context.read<MarketBloc>().add(MarketEventLoadProducts());
          return const Center(child: CircularProgressIndicator());
        }

        // إذا كان التحميل جارٍ، أظهر مؤشر التحميل
        else if (state is MarketStateLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        // إذا كان هناك خطأ، أظهر رسالة الخطأ
        else if (state is MarketStateError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, size: 48, color: Colors.red),
                const SizedBox(height: 16),
                Text(state.message),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () =>
                      context.read<MarketBloc>().add(MarketEventLoadProducts()),
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        }

        // إذا تم تحميل المنتجات، قم بعرضها
        else if (state is MarketStateProductsLoaded) {
          final products = state.products;

          // إذا لم تكن هناك منتجات، أظهر رسالة
          if (products.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    FontAwesomeIcons.box,
                    size: 48,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'لا توجد منتجات متاحة حالياً',
                    style: TextStyle(fontSize: 18),
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton.icon(
                    icon: const Icon(Icons.add),
                    label: const Text('إضافة منتج جديد'),
                    onPressed: () {
                      final authState = context.read<AuthBloc>().state;
                      if (authState is! AuthStateAuthenticated) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                              content: Text('يجب تسجيل الدخول أولاً')),
                        );
                        return;
                      }

                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const AddProductScreen(),
                        ),
                      );
                    },
                  ),
                ],
              ),
            );
          }

          // عرض قائمة المنتجات
          return RefreshIndicator(
            onRefresh: () async {
              context.read<MarketBloc>().add(MarketEventLoadProducts());
            },
            child: GridView.builder(
              padding: const EdgeInsets.all(16),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 0.75,
              ),
              itemCount: products.length,
              itemBuilder: (context, index) {
                final product = products[index];

                return ProductCard(
                  product: product,
                  onTap: () {
                    // تخزين مرجع لـ MarketBloc قبل الانتقال
                    final marketBloc = context.read<MarketBloc>();
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            ProductDetailScreen(productId: product.id),
                      ),
                    ).then((_) {
                      // استخدام المرجع المخزن بدلاً من context.read
                      marketBloc.add(MarketEventLoadProducts());
                    });
                  },
                );
              },
            ),
          );
        }

        // الحالة الافتراضية
        return const Center(child: CircularProgressIndicator());
      },
    );
  }
}

class ProductDetailScreen extends StatelessWidget {
  final String productId;

  const ProductDetailScreen({super.key, required this.productId});

  void _showDeleteConfirmation(BuildContext context, String productId) {
    // تخزين مراجع للكائنات المطلوبة قبل العملية غير المتزامنة
    final marketBloc = context.read<MarketBloc>();
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final navigator = Navigator.of(context);

    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('حذف المنتج'),
        content: const Text('هل أنت متأكد من رغبتك في حذف هذا المنتج؟'),
        actions: [
          TextButton(
            onPressed: () => navigator.pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            onPressed: () {
              // إغلاق مربع الحوار
              navigator.pop();

              // حذف المنتج باستخدام المرجع المخزن
              marketBloc.add(MarketEventDeleteProduct(productId));

              // عرض شريط إشعار باستخدام المرجع المخزن
              scaffoldMessenger.showSnackBar(
                const SnackBar(content: Text('جاري حذف المنتج...')),
              );

              // الانتقال إلى الشاشة السابقة بعد تأخير قصير
              Future.delayed(const Duration(milliseconds: 500), () {
                // استخدام مرجع التنقل المخزن
                navigator.pop();
              });
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // تحميل تفاصيل المنتج من قاعدة البيانات
    context.read<MarketBloc>().add(MarketEventLoadProductDetails(productId));

    return Scaffold(
      appBar: SimpleUnifiedAppBar(
        title: 'تفاصيل المنتج',
      ),
      body: BlocBuilder<MarketBloc, MarketState>(
        builder: (context, state) {
          if (state is MarketStateLoading) {
            return const Center(child: CircularProgressIndicator());
          } else if (state is MarketStateError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 48, color: Colors.red),
                  const SizedBox(height: 16),
                  Text(state.message),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => context
                        .read<MarketBloc>()
                        .add(MarketEventLoadProductDetails(productId)),
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          } else if (state is MarketStateProductDetailsLoaded) {
            final product = state.product;

            return Stack(
              children: [
                ListView(
                  padding: const EdgeInsets.only(bottom: 80),
                  children: [
                    // معرض صور المنتج
                    SizedBox(
                      height: 250,
                      child: PageView.builder(
                        itemCount: product.imageUrls.isNotEmpty
                            ? product.imageUrls.length
                            : 1,
                        itemBuilder: (context, index) {
                          return product.imageUrls.isNotEmpty
                              ? Image.network(
                                  product.imageUrls[index],
                                  width: double.infinity,
                                  fit: BoxFit.cover,
                                  errorBuilder: (_, __, ___) => Container(
                                    color: Colors.grey[300],
                                    child: const Icon(Icons.image_not_supported,
                                        color: Colors.grey, size: 48),
                                  ),
                                  loadingBuilder:
                                      (context, child, loadingProgress) {
                                    if (loadingProgress == null) return child;
                                    return Container(
                                      color: Colors.grey[300],
                                      child: Center(
                                        child: CircularProgressIndicator(
                                          value: loadingProgress
                                                      .expectedTotalBytes !=
                                                  null
                                              ? loadingProgress
                                                      .cumulativeBytesLoaded /
                                                  loadingProgress
                                                      .expectedTotalBytes!
                                              : null,
                                        ),
                                      ),
                                    );
                                  },
                                )
                              : Container(
                                  color: Colors.grey[300],
                                  child: const Icon(Icons.image,
                                      color: Colors.grey, size: 48),
                                );
                        },
                      ),
                    ),

                    // معلومات المنتج
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Text(
                                  product.name,
                                  style: const TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              Text(
                                '${product.price} ريال/${product.unit}',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w500,
                                  color: Theme.of(context).primaryColor,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),

                          Row(
                            children: [
                              Icon(
                                Icons.category,
                                size: 16,
                                color: Theme.of(context)
                                    .colorScheme
                                    .onSurface
                                    .withOpacity(0.6),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                product.category,
                                style: TextStyle(
                                  color: Theme.of(context)
                                      .colorScheme
                                      .onSurface
                                      .withOpacity(0.6),
                                ),
                              ),
                              const SizedBox(width: 16),
                              Icon(
                                Icons.inventory,
                                size: 16,
                                color: Theme.of(context)
                                    .colorScheme
                                    .onSurface
                                    .withOpacity(0.6),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'متوفر: ${product.quantity} ${product.unit}',
                                style: TextStyle(
                                  color: Theme.of(context)
                                      .colorScheme
                                      .onSurface
                                      .withOpacity(0.6),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),

                          // الوصف
                          const Text(
                            'وصف المنتج',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(product.description),
                          const SizedBox(height: 16),

                          // معلومات البائع
                          const Text(
                            'معلومات البائع',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Card(
                            child: Padding(
                              padding: const EdgeInsets.all(12.0),
                              child: Row(
                                children: [
                                  CircleAvatar(
                                    child: Text(
                                      (product.sellerName ?? 'البائع')
                                          .substring(0, 1),
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          product.sellerName ?? 'البائع',
                                          style: const TextStyle(
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                        Row(
                                          children: [
                                            Icon(
                                              Icons.location_on,
                                              size: 12,
                                              color: Theme.of(context)
                                                  .colorScheme
                                                  .onSurface
                                                  .withOpacity(0.6),
                                            ),
                                            const SizedBox(width: 4),
                                            Text(
                                              product.location,
                                              style: TextStyle(
                                                fontSize: 12,
                                                color: Theme.of(context)
                                                    .colorScheme
                                                    .onSurface
                                                    .withOpacity(0.6),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                  if (product.sellerPhone != null)
                                    TextButton.icon(
                                      icon: const Icon(Icons.phone),
                                      label: const Text('اتصال'),
                                      onPressed: () {
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          SnackBar(
                                              content: Text(
                                                  'الاتصال بـ ${product.sellerPhone}')),
                                        );
                                      },
                                    ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                // أزرار أسفل الشاشة
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: ProductActionButtons(
                      product: product,
                      onDelete: () =>
                          _showDeleteConfirmation(context, product.id)),
                ),
              ],
            );
          }

          return const Center(child: CircularProgressIndicator());
        },
      ),
    );
  }
}

// شاشة إضافة منتج جديد
class AddProductScreen extends StatefulWidget {
  const AddProductScreen({super.key});

  @override
  State<AddProductScreen> createState() => _AddProductScreenState();
}

class _AddProductScreenState extends State<AddProductScreen> {
  final _formKey = GlobalKey<FormBuilderState>();
  final List<TempFileData> _selectedImages = [];

  @override
  Widget build(BuildContext context) {
    // استخدام متغير لتخزين حالة المستخدم
    final authState = context.read<AuthBloc>().state;
    final currentUser =
        authState is AuthStateAuthenticated ? authState.user : null;

    if (currentUser == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('إضافة منتج جديد'),
        ),
        body: const Center(
          child: Text('يجب تسجيل الدخول لإضافة منتج'),
        ),
      );
    }

    return Scaffold(
      appBar: const SimpleUnifiedAppBar(
        title: 'إضافة منتج جديد',
      ),
      body: BlocListener<MarketBloc, MarketState>(
        listener: (context, state) {
          if (state is MarketStateError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(state.message)),
            );
          } else if (state is MarketStateProductAdded) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('تم إضافة المنتج بنجاح')),
            );
            Navigator.pop(context);
          }
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: FormBuilder(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // منطقة اختيار الصور
                const Text(
                  'صور المنتج',
                  style: TextStyle(fontWeight: FontWeight.w500),
                ),
                const SizedBox(height: 8),
                GestureDetector(
                  onTap: _pickImages,
                  child: Container(
                    height: 120,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: Colors.grey.withOpacity(0.5),
                        width: 1.0,
                        style: BorderStyle.solid,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: _selectedImages.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.camera_alt,
                                  size: 40,
                                  color: Colors.grey.withOpacity(0.5),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'انقر لاختيار صور',
                                  style: TextStyle(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurface
                                        .withOpacity(0.6),
                                  ),
                                ),
                              ],
                            ),
                          )
                        : ListView.builder(
                            scrollDirection: Axis.horizontal,
                            padding: const EdgeInsets.all(8),
                            itemCount:
                                _selectedImages.length + 1, // +1 لزر الإضافة
                            itemBuilder: (context, index) {
                              if (index == _selectedImages.length) {
                                return GestureDetector(
                                  onTap: _pickImages,
                                  child: Container(
                                    width: 80,
                                    margin: const EdgeInsets.only(right: 8),
                                    decoration: BoxDecoration(
                                      border: Border.all(
                                        color: Colors.grey.withOpacity(0.5),
                                        width: 1.0,
                                        style: BorderStyle.solid,
                                      ),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Icon(
                                      Icons.add,
                                      size: 40,
                                      color: Colors.grey.withOpacity(0.5),
                                    ),
                                  ),
                                );
                              }

                              return Stack(
                                children: [
                                  Container(
                                    width: 80,
                                    height: 80,
                                    margin: const EdgeInsets.only(right: 8),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      color: Colors.grey.withOpacity(0.2),
                                    ),
                                    child: const Icon(Icons.image, size: 40),
                                  ),
                                  Positioned(
                                    top: 0,
                                    right: 0,
                                    child: GestureDetector(
                                      onTap: () {
                                        setState(() {
                                          _selectedImages.removeAt(index);
                                        });
                                      },
                                      child: Container(
                                        width: 24,
                                        height: 24,
                                        decoration: const BoxDecoration(
                                          color: Colors.red,
                                          shape: BoxShape.circle,
                                        ),
                                        child: const Icon(
                                          Icons.close,
                                          color: Colors.white,
                                          size: 16,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),
                  ),
                ),
                const SizedBox(height: 16),

                // حقول المنتج
                FormBuilderTextField(
                  name: 'name',
                  decoration: const InputDecoration(
                    labelText: 'اسم المنتج',
                    hintText: 'مثال: طماطم طازجة',
                  ),
                  validator: FormBuilderValidators.compose([
                    FormBuilderValidators.required(
                        errorText: 'الرجاء إدخال اسم المنتج'),
                  ]),
                ),
                const SizedBox(height: 16),

                FormBuilderTextField(
                  name: 'description',
                  decoration: const InputDecoration(
                    labelText: 'وصف المنتج',
                    hintText: 'طماطم طازجة من المزرعة...',
                  ),
                  maxLines: 3,
                  validator: FormBuilderValidators.compose([
                    FormBuilderValidators.required(
                        errorText: 'الرجاء إدخال وصف المنتج'),
                  ]),
                ),
                const SizedBox(height: 16),

                Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: FormBuilderTextField(
                        name: 'price',
                        keyboardType: TextInputType.number,
                        decoration: const InputDecoration(
                          labelText: 'السعر',
                          hintText: '1000',
                        ),
                        validator: FormBuilderValidators.compose([
                          FormBuilderValidators.required(
                              errorText: 'الرجاء إدخال السعر'),
                          FormBuilderValidators.numeric(
                              errorText: 'الرجاء إدخال رقم صحيح'),
                        ]),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: FormBuilderDropdown<String>(
                        name: 'unit',
                        decoration: const InputDecoration(
                          labelText: 'الوحدة',
                        ),
                        items: const [
                          DropdownMenuItem(
                            value: 'كجم',
                            child: Text('كجم'),
                          ),
                          DropdownMenuItem(
                            value: 'طن',
                            child: Text('طن'),
                          ),
                          DropdownMenuItem(
                            value: 'قطعة',
                            child: Text('قطعة'),
                          ),
                        ],
                        initialValue: 'كجم',
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                FormBuilderTextField(
                  name: 'quantity',
                  keyboardType: TextInputType.number,
                  decoration: const InputDecoration(
                    labelText: 'الكمية المتوفرة',
                    hintText: '50',
                  ),
                  validator: FormBuilderValidators.compose([
                    FormBuilderValidators.required(
                        errorText: 'الرجاء إدخال الكمية'),
                    FormBuilderValidators.numeric(
                        errorText: 'الرجاء إدخال رقم صحيح'),
                  ]),
                ),
                const SizedBox(height: 16),

                FormBuilderTextField(
                  name: 'location',
                  decoration: const InputDecoration(
                    labelText: 'الموقع',
                    hintText: 'صنعاء - شارع تعز',
                  ),
                  validator: FormBuilderValidators.compose([
                    FormBuilderValidators.required(
                        errorText: 'الرجاء إدخال الموقع'),
                  ]),
                ),
                const SizedBox(height: 16),

                FormBuilderDropdown<String>(
                  name: 'category',
                  decoration: const InputDecoration(
                    labelText: 'الفئة',
                  ),
                  items: const [
                    DropdownMenuItem(
                      value: 'طماطم',
                      child: Text('طماطم'),
                    ),
                    DropdownMenuItem(
                      value: 'قمح',
                      child: Text('قمح'),
                    ),
                    DropdownMenuItem(
                      value: 'بطاطا',
                      child: Text('بطاطا'),
                    ),
                    DropdownMenuItem(
                      value: 'بصل',
                      child: Text('بصل'),
                    ),
                    DropdownMenuItem(
                      value: 'تفاح',
                      child: Text('تفاح'),
                    ),
                    DropdownMenuItem(
                      value: 'بذور',
                      child: Text('بذور'),
                    ),
                    DropdownMenuItem(
                      value: 'أسمدة',
                      child: Text('أسمدة'),
                    ),
                    DropdownMenuItem(
                      value: 'معدات',
                      child: Text('معدات'),
                    ),
                  ],
                  validator: FormBuilderValidators.compose([
                    FormBuilderValidators.required(
                        errorText: 'الرجاء اختيار الفئة'),
                  ]),
                ),
                const SizedBox(height: 24),

                // زر الإضافة
                SizedBox(
                  width: double.infinity,
                  child: BlocBuilder<MarketBloc, MarketState>(
                    builder: (context, state) {
                      return ElevatedButton(
                        onPressed:
                            state is MarketStateLoading ? null : _submitForm,
                        child: Padding(
                          padding: const EdgeInsets.all(12.0),
                          child: state is MarketStateLoading
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2,
                                  ),
                                )
                              : const Text(
                                  'إضافة المنتج',
                                  style: TextStyle(fontSize: 16),
                                ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _pickImages() async {
    final ImagePicker picker = ImagePicker();
    // Store a reference to the scaffold messenger
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    try {
      final XFile? image = await picker.pickImage(source: ImageSource.gallery);
      if (image != null && mounted) {
        // قراءة بيانات الصورة
        final bytes = await image.readAsBytes();

        // إضافة الصورة إلى قائمة الصور الجديدة
        setState(() {
          _selectedImages.add(
            TempFileData(
              bytes: bytes,
              fileName: 'image${_selectedImages.length + 1}.jpg',
              mimeType: 'image/jpeg',
            ),
          );
        });
      }
    } catch (e) {
      // Check if the widget is still mounted before showing a snackbar
      if (mounted) {
        scaffoldMessenger.showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    }
  }

  void _submitForm() {
    if (_formKey.currentState?.saveAndValidate() ?? false) {
      final authState = context.read<AuthBloc>().state;
      if (authState is! AuthStateAuthenticated) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('يجب تسجيل الدخول لإضافة منتج')),
        );
        return;
      }

      final formData = _formKey.currentState!.value;
      final currentUser = authState.user;

      if (_selectedImages.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('الرجاء إضافة صورة واحدة على الأقل')),
        );
        return;
      }

      // إظهار رسالة جاري الإضافة
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('جاري إضافة المنتج...')),
      );

      // إضافة المنتج باستخدام MarketBloc
      context.read<MarketBloc>().add(
            MarketEventAddProduct(
              name: formData['name'],
              description: formData['description'],
              price: double.parse(formData['price']),
              unit: formData['unit'] ?? 'كجم',
              quantity: double.parse(formData['quantity']),
              category: formData['category'] ?? 'طماطم',
              images: _selectedImages,
              location: formData['location'],
              userId: currentUser.id,
              userName: currentUser.name,
              userPhone: currentUser.phone,
            ),
          );

      // العودة إلى الشاشة الرئيسية بعد إضافة المنتج
      Navigator.pop(context);
    }
  }
}

// شاشة قائمة المحادثات
class ChatsListScreen extends StatefulWidget {
  const ChatsListScreen({super.key});

  @override
  State<ChatsListScreen> createState() => _ChatsListScreenState();
}

class _ChatsListScreenState extends State<ChatsListScreen> {
  @override
  void initState() {
    super.initState();
    // تحميل المحادثات عند تهيئة الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final authState = context.read<AuthBloc>().state;
        final currentUser =
            authState is AuthStateAuthenticated ? authState.user : null;

        if (currentUser != null) {
          // إرسال حدث تحميل المحادثات إلى MarketBloc
          context.read<MarketBloc>().add(MarketEventLoadChats(currentUser.id));
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // استخدام متغير لتخزين حالة المستخدم
    final authState = context.read<AuthBloc>().state;
    final currentUser =
        authState is AuthStateAuthenticated ? authState.user : null;

    if (currentUser == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.chat_bubble_outline, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            const Text(
              'قم بتسجيل الدخول لعرض المحادثات',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                // عودة إلى شاشة تسجيل الدخول أو إظهار نافذة تسجيل الدخول
              },
              child: const Text('تسجيل الدخول'),
            ),
          ],
        ),
      );
    }

    return BlocConsumer<MarketBloc, MarketState>(
      listener: (context, state) {
        // عرض رسالة خطأ إذا فشل تحميل المحادثات
        if (state is MarketStateError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
        }
      },
      builder: (context, state) {
        // إذا كانت الحالة الأولية، قم بتحميل المحادثات
        if (state is MarketStateInitial) {
          context.read<MarketBloc>().add(MarketEventLoadChats(currentUser.id));
          return const Center(child: CircularProgressIndicator());
        }

        // إذا كان التحميل جارٍ، أظهر مؤشر التحميل
        else if (state is MarketStateLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        // إذا تم تحميل المحادثات، قم بعرضها
        else if (state is MarketStateChatsLoaded) {
          final chats = state.chats;

          // إذا لم تكن هناك محادثات، أظهر رسالة
          if (chats.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.chat_bubble_outline,
                      size: 64, color: Colors.grey),
                  const SizedBox(height: 16),
                  const Text(
                    'لا توجد محادثات',
                    style: TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      // إعادة تحميل المحادثات
                      context
                          .read<MarketBloc>()
                          .add(MarketEventLoadChats(currentUser.id));
                    },
                    child: const Text('تحديث'),
                  ),
                ],
              ),
            );
          }

          // عرض قائمة المحادثات
          return RefreshIndicator(
            onRefresh: () async {
              context
                  .read<MarketBloc>()
                  .add(MarketEventLoadChats(currentUser.id));
            },
            child: ListView.separated(
              padding: const EdgeInsets.all(16),
              itemCount: chats.length,
              separatorBuilder: (context, index) => const Divider(height: 1),
              itemBuilder: (context, index) {
                final chat = chats[index];

                // تحديد معرف المستخدم الآخر في المحادثة
                final otherUserId = chat.participants.firstWhere(
                  (id) => id != currentUser.id,
                  orElse: () => 'غير معروف',
                );

                // تحديد اسم المستخدم الآخر (يمكن تحسينه لاحقاً بجلب بيانات المستخدم)
                final otherUserName =
                    'المستخدم ${otherUserId.substring(0, min(otherUserId.length, 4))}';

                // تحديد عدد الرسائل غير المقروءة
                final unreadCount = chat.unreadCount[currentUser.id] ?? 0;

                // تنسيق وقت آخر رسالة
                final lastMessageTime = _formatChatTime(chat.lastMessageTime);

                return ListTile(
                  contentPadding:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                  leading: CircleAvatar(
                    child: Text(otherUserName.substring(0, 1)),
                  ),
                  title: Text(
                    otherUserName,
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                  subtitle: Text(
                    chat.lastMessage,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  trailing: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        lastMessageTime,
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withOpacity(0.6),
                        ),
                      ),
                      const SizedBox(height: 4),
                      if (unreadCount > 0)
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            '$unreadCount',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                          ),
                        ),
                    ],
                  ),
                  onTap: () {
                    // تخزين معرف المستخدم الحالي قبل الانتقال
                    final userId = currentUser.id;
                    // تخزين مرجع لـ MarketBloc قبل الانتقال
                    final marketBloc = context.read<MarketBloc>();

                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => ChatScreen(
                          chatId: chat.id,
                          otherUserId: otherUserId,
                          otherUserName: otherUserName,
                          currentUserId: userId,
                        ),
                      ),
                    ).then((_) {
                      // إعادة تحميل المحادثات عند العودة من شاشة المحادثة
                      // استخدام المرجع المخزن بدلاً من context.read
                      marketBloc.add(MarketEventLoadChats(userId));
                    });
                  },
                );
              },
            ),
          );
        }

        // الحالة الافتراضية
        return const Center(child: CircularProgressIndicator());
      },
    );
  }

  // دالة لتنسيق وقت المحادثة
  String _formatChatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${time.year}/${time.month}/${time.day}';
    }
  }
}

// شاشة المحادثة
class ChatScreen extends StatefulWidget {
  final String chatId;
  final String otherUserId;
  final String otherUserName;
  final String currentUserId;
  final String? initialMessage;

  const ChatScreen({
    super.key,
    required this.chatId,
    required this.otherUserId,
    required this.otherUserName,
    required this.currentUserId,
    this.initialMessage,
  });

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  bool _isSending = false;

  @override
  void initState() {
    super.initState();

    // إرسال رسالة أولية إذا كانت موجودة
    if (widget.initialMessage != null) {
      _messageController.text = widget.initialMessage!;
    }

    // تحميل الرسائل من Firebase
    _loadMessages();

    // تعليم الرسائل كمقروءة إذا كانت محادثة موجودة
    if (widget.chatId != 'new') {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          // تخزين مرجع لـ MarketBloc قبل العملية غير المتزامنة
          final marketBloc = context.read<MarketBloc>();
          // تعليم الرسائل كمقروءة
          marketBloc.marketRepository
              .markMessagesAsRead(widget.chatId, widget.currentUserId);
        }
      });
    }
  }

  void _loadMessages() {
    // إذا كانت محادثة جديدة، فلا توجد رسائل سابقة
    if (widget.chatId == 'new') {
      return;
    }

    // تحميل الرسائل من Firebase
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        context.read<MarketBloc>().add(MarketEventLoadMessages(widget.chatId));
      }
    });
  }

  @override
  void dispose() {
    _messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.otherUserName),
        actions: [
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () {
              // عرض خيارات المحادثة
              showModalBottomSheet(
                context: context,
                builder: (context) => Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ListTile(
                      leading: const Icon(Icons.delete),
                      title: const Text('حذف المحادثة'),
                      onTap: () {
                        Navigator.pop(context);
                        // حذف المحادثة (سيتم تنفيذها لاحقاً)
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                              content:
                                  Text('سيتم إضافة خاصية حذف المحادثة قريباً')),
                        );
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.block),
                      title: const Text('حظر المستخدم'),
                      onTap: () {
                        Navigator.pop(context);
                        // حظر المستخدم (سيتم تنفيذها لاحقاً)
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                              content:
                                  Text('سيتم إضافة خاصية حظر المستخدم قريباً')),
                        );
                      },
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // قائمة الرسائل
          Expanded(
            child: BlocConsumer<MarketBloc, MarketState>(
              listener: (context, state) {
                // عرض رسالة خطأ إذا فشل تحميل الرسائل
                if (state is MarketStateError) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text(state.message)),
                  );
                }
                // إذا تم إرسال رسالة بنجاح، قم بتحميل الرسائل مرة أخرى
                else if (state is MarketStateMessageSent) {
                  // إعادة تحميل الرسائل بعد إرسال رسالة جديدة
                  context
                      .read<MarketBloc>()
                      .add(MarketEventLoadMessages(state.message.chatId));
                  // إلغاء حالة الإرسال
                  setState(() {
                    _isSending = false;
                  });
                }
              },
              builder: (context, state) {
                // إذا كانت محادثة جديدة، أظهر رسالة
                if (widget.chatId == 'new') {
                  return const Center(
                    child: Text('ابدأ المحادثة عن طريق إرسال رسالة'),
                  );
                }

                // إذا كان التحميل جارٍ، أظهر مؤشر التحميل
                if (state is MarketStateLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                // إذا تم تحميل الرسائل، قم بعرضها
                else if (state is MarketStateMessagesLoaded &&
                    state.chatId == widget.chatId) {
                  final messages = state.messages;

                  // إذا لم تكن هناك رسائل، أظهر رسالة
                  if (messages.isEmpty) {
                    return const Center(
                      child: Text('ابدأ المحادثة عن طريق إرسال رسالة'),
                    );
                  }

                  // عرض قائمة الرسائل
                  return ListView.builder(
                    padding: const EdgeInsets.all(16),
                    reverse: true,
                    itemCount: messages.length,
                    itemBuilder: (context, index) {
                      final reversedIndex = messages.length - 1 - index;
                      final message = messages[reversedIndex];
                      final isSender = message.senderId == widget.currentUserId;

                      return Align(
                        alignment: isSender
                            ? Alignment.centerRight
                            : Alignment.centerLeft,
                        child: Container(
                          margin: const EdgeInsets.only(bottom: 12),
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: isSender
                                ? Theme.of(context)
                                    .primaryColor
                                    .withOpacity(0.2)
                                : Theme.of(context).cardColor,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          constraints: BoxConstraints(
                            maxWidth: MediaQuery.of(context).size.width * 0.7,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(message.content),
                              const SizedBox(height: 4),
                              Text(
                                DateFormat('HH:mm').format(message.timestamp),
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Theme.of(context)
                                      .colorScheme
                                      .onSurface
                                      .withOpacity(0.6),
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  );
                }

                // الحالة الافتراضية
                return const Center(child: CircularProgressIndicator());
              },
            ),
          ),

          // منطقة إرسال الرسائل
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.attach_file),
                  onPressed: _isSending
                      ? null
                      : () {
                          // عرض خيارات المرفقات
                          showModalBottomSheet(
                            context: context,
                            builder: (context) => Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                ListTile(
                                  leading: const Icon(Icons.image),
                                  title: const Text('إرسال صورة'),
                                  onTap: () {
                                    Navigator.pop(context);

                                    // محاكاة إرسال صورة
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                          content: Text(
                                              'سيتم إضافة خاصية إرسال الصور قريباً')),
                                    );
                                  },
                                ),
                                ListTile(
                                  leading: const Icon(Icons.camera_alt),
                                  title: const Text('التقاط صورة'),
                                  onTap: () {
                                    Navigator.pop(context);

                                    // محاكاة التقاط صورة
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                          content: Text(
                                              'سيتم إضافة خاصية التقاط الصور قريباً')),
                                    );
                                  },
                                ),
                              ],
                            ),
                          );
                        },
                ),
                Expanded(
                  child: TextField(
                    controller: _messageController,
                    decoration: const InputDecoration(
                      hintText: 'اكتب رسالة...',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: null,
                    enabled: !_isSending,
                  ),
                ),
                const SizedBox(width: 12),
                _isSending
                    ? const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : IconButton(
                        icon: const Icon(Icons.send),
                        onPressed: _sendMessage,
                        color: Theme.of(context).primaryColor,
                      ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _sendMessage() {
    if (_messageController.text.trim().isEmpty || _isSending) {
      return;
    }

    // تعيين حالة الإرسال
    setState(() {
      _isSending = true;
    });

    // الحصول على نص الرسالة
    final messageText = _messageController.text.trim();
    _messageController.clear();

    // إرسال الرسالة باستخدام MarketBloc
    context.read<MarketBloc>().add(
          MarketEventSendMessage(
            chatId: widget.chatId,
            content: messageText,
            senderId: widget.currentUserId,
            participants: [widget.currentUserId, widget.otherUserId],
          ),
        );
  }
}

// Widget منفصل لأزرار المنتج
class ProductActionButtons extends StatelessWidget {
  final Product product;
  final VoidCallback onDelete;

  const ProductActionButtons({
    super.key,
    required this.product,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    // استخدام متغير لتخزين حالة المستخدم
    final authState = context.read<AuthBloc>().state;
    final currentUser =
        authState is AuthStateAuthenticated ? authState.user : null;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: currentUser != null && currentUser.id == product.sellerId
          ? Row(
              // إذا كان المستخدم هو صاحب المنتج، نعرض أزرار التعديل والحذف
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    icon: const Icon(Icons.edit),
                    label: const Text('تعديل'),
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) =>
                              EditProductScreen(product: product),
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                    ),
                    icon: const Icon(Icons.delete),
                    label: const Text('حذف'),
                    onPressed: onDelete,
                  ),
                ),
              ],
            )
          : Row(
              // إذا كان المستخدم زائر أو مستخدم آخر
              children: [
                // لا نعرض زر المحادثة إذا كان المنتج للمستخدم الحالي
                if (currentUser == null ||
                    currentUser.id != product.sellerId) ...[
                  Expanded(
                    child: OutlinedButton.icon(
                      icon: const Icon(Icons.chat),
                      label: const Text('محادثة'),
                      onPressed: () {
                        if (currentUser == null) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                                content: Text('يجب تسجيل الدخول للمحادثة')),
                          );
                          return;
                        }

                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => ChatScreen(
                              chatId: 'new',
                              otherUserId: product.sellerId,
                              otherUserName: product.sellerName ?? 'البائع',
                              currentUserId: currentUser.id,
                              initialMessage:
                                  'مرحباً، أنا مهتم بالمنتج "${product.name}"',
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  const SizedBox(width: 12),
                ],
                Expanded(
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.shopping_cart),
                    label: const Text('شراء'),
                    onPressed: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                            content: Text('تم إضافة المنتج إلى سلة المشتريات')),
                      );
                    },
                  ),
                ),
              ],
            ),
    );
  }
}
