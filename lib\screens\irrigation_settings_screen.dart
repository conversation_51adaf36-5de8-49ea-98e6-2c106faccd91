import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:sam03/bloc/irrigation_bloc.dart';
import 'package:sam03/models/app_models.dart';
import 'package:sam03/core/constants/app_constants.dart';

/// شاشة إعدادات نظام الري المتقدمة
class IrrigationSettingsScreen extends StatefulWidget {
  final IrrigationSystem system;
  final IrrigationSettings? currentSettings;

  const IrrigationSettingsScreen({
    super.key,
    required this.system,
    this.currentSettings,
  });

  @override
  State<IrrigationSettingsScreen> createState() =>
      _IrrigationSettingsScreenState();
}

class _IrrigationSettingsScreenState extends State<IrrigationSettingsScreen> {
  final _formKey = GlobalKey<FormState>();

  // متحكمات النصوص
  final _moistureThresholdController = TextEditingController();
  final _temperatureMaxController = TextEditingController();
  final _waterLevelMinController = TextEditingController();
  final _defaultDurationController = TextEditingController();
  final _maxWaterPerDayController = TextEditingController();
  final _hysteresisMarginController = TextEditingController();

  // متغيرات الحالة
  String _operationMode = AppConstants.operationModeManual;
  bool _ignoreRain = false;
  bool _rainSensorEnabled = true;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeSettings();
  }

  void _initializeSettings() {
    final settings = widget.currentSettings;
    if (settings != null) {
      _moistureThresholdController.text =
          (settings.moistureThreshold * 100).toStringAsFixed(0);
      _temperatureMaxController.text =
          settings.temperatureMax.toStringAsFixed(1);
      _waterLevelMinController.text =
          (settings.waterLevelMin * 100).toStringAsFixed(0);
      _defaultDurationController.text = settings.defaultDuration.toString();
      _maxWaterPerDayController.text =
          settings.maxWaterPerDay.toStringAsFixed(0);
      _hysteresisMarginController.text =
          (settings.hysteresisMargin * 100).toStringAsFixed(0);
      _operationMode = settings.operationMode;
      _ignoreRain = settings.ignoreRain;
      _rainSensorEnabled = settings.rainSensorEnabled;
    } else {
      // القيم الافتراضية
      _moistureThresholdController.text =
          (AppConstants.defaultMoistureThreshold * 100).toStringAsFixed(0);
      _temperatureMaxController.text =
          AppConstants.defaultTemperatureMax.toStringAsFixed(1);
      _waterLevelMinController.text =
          (AppConstants.defaultWaterLevelMin * 100).toStringAsFixed(0);
      _defaultDurationController.text =
          AppConstants.defaultIrrigationDuration.toString();
      _maxWaterPerDayController.text =
          AppConstants.defaultMaxWaterPerDay.toStringAsFixed(0);
      _hysteresisMarginController.text =
          (AppConstants.defaultHysteresisMargin * 100).toStringAsFixed(0);
      _operationMode = AppConstants.operationModeManual;
      _ignoreRain = AppConstants.defaultIgnoreRain;
      _rainSensorEnabled = true;
    }
  }

  @override
  void dispose() {
    _moistureThresholdController.dispose();
    _temperatureMaxController.dispose();
    _waterLevelMinController.dispose();
    _defaultDurationController.dispose();
    _maxWaterPerDayController.dispose();
    _hysteresisMarginController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('إعدادات ${widget.system.name}'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: _showHelpDialog,
          ),
        ],
      ),
      body: BlocListener<IrrigationBloc, IrrigationState>(
        listener: (context, state) {
          if (state is IrrigationStateSettingsUpdated) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم حفظ الإعدادات بنجاح'),
                backgroundColor: Colors.green,
              ),
            );
            Navigator.pop(context);
          } else if (state is IrrigationStateError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
            setState(() {
              _isLoading = false;
            });
          }
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // معلومات النظام
                _buildSystemInfoCard(),
                const SizedBox(height: 20),

                // وضع التشغيل
                _buildOperationModeSection(),
                const SizedBox(height: 20),

                // إعدادات الحساسات
                _buildSensorSettingsSection(),
                const SizedBox(height: 20),

                // إعدادات الري
                _buildIrrigationSettingsSection(),
                const SizedBox(height: 20),

                // إعدادات متقدمة
                _buildAdvancedSettingsSection(),
                const SizedBox(height: 32),

                // أزرار الإجراءات
                _buildActionButtons(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSystemInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _getSystemIcon(widget.system.type),
                  color: _getSystemColor(widget.system.type),
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.system.name,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'النوع: ${_getSystemTypeName(widget.system.type)}',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                        ),
                      ),
                      if (widget.system.location != null)
                        Text(
                          'الموقع: ${widget.system.location}',
                          style: TextStyle(
                            color: Colors.grey.shade600,
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOperationModeSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'وضع التشغيل',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Column(
              children: [
                _buildModeOption(
                  AppConstants.operationModeManual,
                  'يدوي',
                  'التحكم اليدوي الكامل في عملية الري',
                  Icons.touch_app,
                ),
                _buildModeOption(
                  AppConstants.operationModeSmart,
                  'ذكي',
                  'الري التلقائي بناءً على قراءات الحساسات',
                  FontAwesomeIcons.wandMagicSparkles,
                ),
                _buildModeOption(
                  AppConstants.operationModeScheduled,
                  'مجدول',
                  'الري وفقاً لجدول زمني محدد مسبقاً',
                  Icons.schedule,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModeOption(
      String value, String title, String description, IconData icon) {
    final isSelected = _operationMode == value;

    return InkWell(
      onTap: () {
        setState(() {
          _operationMode = value;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          children: [
            Radio<String>(
              value: value,
              groupValue: _operationMode,
              onChanged: (String? newValue) {
                if (newValue != null) {
                  setState(() {
                    _operationMode = newValue;
                  });
                }
              },
            ),
            Icon(icon, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSensorSettingsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إعدادات الحساسات',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildNumberField(
              controller: _moistureThresholdController,
              label: 'عتبة رطوبة التربة (%)',
              hint: '40',
              icon: FontAwesomeIcons.droplet,
              suffix: '%',
              validator: (value) => _validatePercentage(value, 'عتبة الرطوبة'),
            ),
            const SizedBox(height: 16),
            _buildNumberField(
              controller: _temperatureMaxController,
              label: 'الحد الأقصى لدرجة الحرارة (°م)',
              hint: '35.0',
              icon: FontAwesomeIcons.temperatureHalf,
              suffix: '°م',
              validator: (value) => _validateTemperature(value),
            ),
            const SizedBox(height: 16),
            _buildNumberField(
              controller: _waterLevelMinController,
              label: 'الحد الأدنى لمستوى المياه (%)',
              hint: '30',
              icon: FontAwesomeIcons.water,
              suffix: '%',
              validator: (value) => _validatePercentage(value, 'مستوى المياه'),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('تفعيل حساس المطر'),
              subtitle: const Text('إيقاف الري تلقائياً عند هطول الأمطار'),
              value: _rainSensorEnabled,
              onChanged: (bool value) {
                setState(() {
                  _rainSensorEnabled = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIrrigationSettingsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إعدادات الري',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildNumberField(
              controller: _defaultDurationController,
              label: 'مدة الري الافتراضية (دقيقة)',
              hint: '30',
              icon: Icons.timer,
              suffix: 'دقيقة',
              validator: (value) => _validateDuration(value),
            ),
            const SizedBox(height: 16),
            _buildNumberField(
              controller: _maxWaterPerDayController,
              label: 'الحد الأقصى للمياه يومياً (لتر)',
              hint: '500',
              icon: FontAwesomeIcons.bucket,
              suffix: 'لتر',
              validator: (value) => _validateWaterAmount(value),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdvancedSettingsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إعدادات متقدمة',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildNumberField(
              controller: _hysteresisMarginController,
              label: 'هامش الهيستريزس (%)',
              hint: '5',
              icon: Icons.tune,
              suffix: '%',
              validator: (value) => _validateHysteresis(value),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('تجاهل هطول الأمطار'),
              subtitle: const Text('الري حتى لو كان هناك مطر'),
              value: _ignoreRain,
              onChanged: (bool value) {
                setState(() {
                  _ignoreRain = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNumberField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    required String suffix,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: TextInputType.number,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon),
        suffixText: suffix,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        filled: true,
        fillColor: Colors.grey.shade50,
      ),
      validator: validator,
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _saveSettings,
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text('حفظ الإعدادات'),
          ),
        ),
      ],
    );
  }

  // ===== دوال التحقق =====

  String? _validatePercentage(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال $fieldName';
    }
    final number = double.tryParse(value);
    if (number == null) {
      return 'يرجى إدخال رقم صحيح';
    }
    if (number < 0 || number > 100) {
      return 'يجب أن تكون القيمة بين 0 و 100';
    }
    return null;
  }

  String? _validateTemperature(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال درجة الحرارة';
    }
    final number = double.tryParse(value);
    if (number == null) {
      return 'يرجى إدخال رقم صحيح';
    }
    if (number < -10 || number > 60) {
      return 'يجب أن تكون درجة الحرارة بين -10 و 60';
    }
    return null;
  }

  String? _validateDuration(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال مدة الري';
    }
    final number = int.tryParse(value);
    if (number == null) {
      return 'يرجى إدخال رقم صحيح';
    }
    if (number < 1 || number > 180) {
      return 'يجب أن تكون المدة بين 1 و 180 دقيقة';
    }
    return null;
  }

  String? _validateWaterAmount(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال كمية المياه';
    }
    final number = double.tryParse(value);
    if (number == null) {
      return 'يرجى إدخال رقم صحيح';
    }
    if (number < 10 || number > 2000) {
      return 'يجب أن تكون الكمية بين 10 و 2000 لتر';
    }
    return null;
  }

  String? _validateHysteresis(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال هامش الهيستريزس';
    }
    final number = double.tryParse(value);
    if (number == null) {
      return 'يرجى إدخال رقم صحيح';
    }
    if (number < 1 || number > 20) {
      return 'يجب أن يكون الهامش بين 1 و 20%';
    }
    return null;
  }

  // ===== دوال مساعدة =====

  Color _getSystemColor(String type) {
    switch (type) {
      case AppConstants.irrigationTypeDrip:
        return Colors.blue;
      case AppConstants.irrigationTypeSprinkler:
        return Colors.green;
      case AppConstants.irrigationTypeMicro:
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  IconData _getSystemIcon(String type) {
    switch (type) {
      case AppConstants.irrigationTypeDrip:
        return FontAwesomeIcons.droplet;
      case AppConstants.irrigationTypeSprinkler:
        return FontAwesomeIcons.shower;
      case AppConstants.irrigationTypeMicro:
        return FontAwesomeIcons.seedling;
      default:
        return FontAwesomeIcons.water;
    }
  }

  String _getSystemTypeName(String type) {
    return AppConstants.irrigationTypeNames[type] ?? 'غير محدد';
  }

  void _saveSettings() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final settings = IrrigationSettings(
      systemId: widget.system.id,
      operationMode: _operationMode,
      moistureThreshold: double.parse(_moistureThresholdController.text) / 100,
      temperatureMax: double.parse(_temperatureMaxController.text),
      waterLevelMin: double.parse(_waterLevelMinController.text) / 100,
      ignoreRain: _ignoreRain,
      hysteresisMargin: double.parse(_hysteresisMarginController.text) / 100,
      defaultDuration: int.parse(_defaultDurationController.text),
      schedule: widget.currentSettings?.schedule ?? [],
      rainSensorEnabled: _rainSensorEnabled,
      maxWaterPerDay: double.parse(_maxWaterPerDayController.text),
      selectedPlantId: widget.currentSettings?.selectedPlantId,
      currentGrowthStage: widget.currentSettings?.currentGrowthStage,
    );

    context.read<IrrigationBloc>().add(
          IrrigationEventUpdateSettings(settings),
        );
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مساعدة الإعدادات'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'وضع التشغيل:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text('• يدوي: تحكم كامل من المستخدم'),
              Text('• ذكي: ري تلقائي حسب الحساسات'),
              Text('• مجدول: ري حسب جدول زمني'),
              SizedBox(height: 12),
              Text(
                'عتبة الرطوبة:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text('النسبة التي يبدأ عندها الري التلقائي'),
              SizedBox(height: 12),
              Text(
                'هامش الهيستريزس:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text('منع التشغيل والإيقاف المتكرر للنظام'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('فهمت'),
          ),
        ],
      ),
    );
  }
}
