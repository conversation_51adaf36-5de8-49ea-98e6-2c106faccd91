/// ثوابت التطبيق
class AppConstants {
  // ===== أنواع أنظمة الري =====
  static const String irrigationTypeDrip = 'drip';
  static const String irrigationTypeSprinkler = 'sprinkler';
  static const String irrigationTypeMicro = 'micro';

  // ===== حالات أنظمة الري =====
  static const String irrigationStatusActive = 'active';
  static const String irrigationStatusInactive = 'inactive';
  static const String irrigationStatusMaintenance = 'maintenance';

  // ===== عتبات الحساسات =====
  static const double moistureThresholdLow = 0.3;
  static const double moistureThresholdMedium = 0.4;
  static const double moistureThresholdHigh = 0.6;

  static const double temperatureThresholdCold = 15.0;
  static const double temperatureThresholdNormal = 25.0;
  static const double temperatureThresholdWarm = 35.0;
  static const double temperatureThresholdMax = 40.0;

  static const double waterLevelThresholdLow = 0.2;
  static const double waterLevelThresholdMedium = 0.4;
  static const double waterLevelThresholdHigh = 0.8;

  static const double batteryThresholdLow = 0.2;
  static const double batteryThresholdMedium = 0.5;

  static const double humidityThresholdLow = 0.3;
  static const double humidityThresholdMedium = 0.5;
  static const double humidityThresholdHigh = 0.7;

  static const double rainfallThresholdLight = 5.0; // mm
  static const double rainfallThresholdModerate = 15.0; // mm
  static const double rainfallThresholdHeavy = 30.0; // mm

  // ===== إعدادات افتراضية =====
  static const int defaultIrrigationDuration = 30; // دقيقة
  static const double defaultWaterFlowRate = 5.0; // لتر/دقيقة
  static const double defaultMaxWaterPerDay = 500.0; // لتر
  static const double defaultMoistureThreshold = 0.4; // 40%
  static const double defaultTemperatureMax = 35.0; // درجة مئوية
  static const double defaultWaterLevelMin = 0.3; // 30%
  static const bool defaultIgnoreRain = false;
  static const double defaultHysteresisMargin = 0.05; // 5% هامش هيستريزس

  // ===== رسائل النظام =====
  static const String messageIrrigationStarted = 'تم بدء الري بنجاح';
  static const String messageIrrigationStopped = 'تم إيقاف الري';
  static const String messageSensorsUpdated = 'تم تحديث قراءات الحساسات';
  static const String messageSystemSelected = 'تم تحديد النظام';
  static const String messageSettingsUpdated = 'تم تحديث الإعدادات';

  // ===== رسائل تشخيص الأمراض =====
  static const String messageImageUploaded = 'تم رفع الصورة بنجاح';
  static const String messageDiagnosisStarted = 'تم بدء التشخيص';
  static const String messageDiagnosisCompleted = 'تم إكمال التشخيص';
  static const String messageTreatmentLoaded = 'تم تحميل خطة العلاج';

  // ===== رسائل النظام المتقدم =====
  static const String messageSmartModeEnabled = 'تم تفعيل الوضع الذكي';
  static const String messageManualModeEnabled = 'تم تفعيل الوضع اليدوي';
  static const String messagePumpStartedAuto = 'تم تشغيل المضخة تلقائياً';
  static const String messagePumpStoppedAuto = 'تم إيقاف المضخة تلقائياً';
  static const String messageSettingsSaved = 'تم حفظ الإعدادات';
  static const String messageSystemAdded = 'تم إضافة النظام بنجاح';
  static const String messageSystemUpdated = 'تم تحديث النظام';
  static const String messageSystemDeleted = 'تم حذف النظام';
  static const String messagePlantSelected = 'تم اختيار النبات';
  static const String messageGrowthStageUpdated = 'تم تحديث مرحلة النمو';

  // ===== رسائل الأخطاء =====
  static const String errorLoadingSystems = 'فشل في تحميل أنظمة الري';
  static const String errorSelectingSystem = 'فشل في تحديد النظام';
  static const String errorUpdatingSensors = 'فشل في تحديث الحساسات';
  static const String errorStartingIrrigation = 'فشل في بدء الري';
  static const String errorStoppingIrrigation = 'فشل في إيقاف الري';
  static const String errorUpdatingSettings = 'فشل في تحديث الإعدادات';
  static const String errorLoadingRecords = 'فشل في تحميل السجلات';
  static const String errorLoadingStats = 'فشل في تحميل الإحصائيات';
  static const String errorSystemNotFound = 'لم يتم العثور على النظام المحدد';

  // ===== رسائل أخطاء تشخيص الأمراض =====
  static const String errorUploadingImage = 'فشل في رفع الصورة';
  static const String errorProcessingDiagnosis = 'فشل في معالجة التشخيص';
  static const String errorLoadingDiseaseInfo = 'فشل في تحميل معلومات المرض';
  static const String errorLoadingDiagnosisHistory =
      'فشل في تحميل تاريخ التشخيص';

  // ===== رسائل التحذيرات والإشعارات =====
  static const String warningLowWaterLevel = 'تحذير: مستوى المياه منخفض';
  static const String warningHighTemperature = 'تحذير: درجة الحرارة مرتفعة';
  static const String warningLowSoilMoisture = 'تحذير: رطوبة التربة منخفضة';
  static const String warningSystemOffline = 'تحذير: النظام غير متصل';
  static const String warningPumpFailure = 'تحذير: خلل في المضخة';
  static const String notificationRainDetected = 'تم رصد هطول أمطار';
  static const String notificationIrrigationScheduled = 'تم جدولة الري';
  static const String notificationMaintenanceRequired = 'مطلوب صيانة النظام';

  // ===== أسماء المجموعات في Firebase =====
  static const String collectionIrrigationSystems = 'irrigation_systems';
  static const String collectionIrrigationRecords = 'irrigation_records';
  static const String collectionIrrigationSettings = 'irrigation_settings';
  static const String collectionSensorReadings = 'sensor_readings';
  static const String collectionPlantTypes = 'plant_types';
  static const String collectionWaterUsageStats = 'water_usage_stats';
  static const String collectionSystemAlerts = 'system_alerts';
  static const String collectionPlantDatabase = 'plant_database';
  static const String collectionGrowthStages = 'growth_stages';
  static const String collectionNotifications = 'notifications';

  // ===== مجموعات تشخيص الأمراض =====
  static const String collectionDiseaseInfo = 'disease_info';
  static const String collectionDiagnosisResults = 'diagnosis_results';
  static const String collectionDiagnosisHistory = 'diagnosis_history';

  // ===== مسارات Realtime Database =====
  static const String realtimePumpControl = 'pump_control';
  static const String realtimeSensorData = 'sensor_data';
  static const String realtimeSystemStatus = 'system_status';

  // ===== مفاتيح التخزين المحلي =====
  static const String keySelectedSystemId = 'selected_system_id';
  static const String keyLastSensorUpdate = 'last_sensor_update';
  static const String keyIrrigationSettings = 'irrigation_settings';

  // ===== حدود التطبيق =====
  static const int maxSystemsPerUser = 10;
  static const int maxRecordsToLoad = 50;
  static const int maxChartDataPoints = 100;
  static const int sensorUpdateIntervalSeconds = 30;
  static const int irrigationTimeoutMinutes = 120;

  // ===== أسماء الأنواع والحالات =====
  static const Map<String, String> irrigationTypeNames = {
    irrigationTypeDrip: 'ري بالتنقيط',
    irrigationTypeSprinkler: 'ري بالرش',
    irrigationTypeMicro: 'ري دقيق',
  };

  static const Map<String, String> irrigationStatusNames = {
    irrigationStatusActive: 'نشط',
    irrigationStatusInactive: 'غير نشط',
    irrigationStatusMaintenance: 'صيانة',
  };

  static const Map<String, String> irrigationTriggerNames = {
    'manual': 'يدوي',
    'auto': 'تلقائي',
    'smart': 'ذكي',
    'scheduled': 'مجدول',
  };

  // ===== أوضاع التشغيل =====
  static const String operationModeManual = 'manual';
  static const String operationModeSmart = 'smart';
  static const String operationModeScheduled = 'scheduled';

  static const Map<String, String> operationModeNames = {
    operationModeManual: 'يدوي',
    operationModeSmart: 'ذكي',
    operationModeScheduled: 'مجدول',
  };

  // ===== مراحل نمو النباتات =====
  static const String growthStageSeedling = 'seedling';
  static const String growthStageVegetative = 'vegetative';
  static const String growthStageFlowering = 'flowering';
  static const String growthStageFruiting = 'fruiting';
  static const String growthStageMaturity = 'maturity';

  static const Map<String, String> growthStageNames = {
    growthStageSeedling: 'مرحلة الإنبات',
    growthStageVegetative: 'مرحلة النمو الخضري',
    growthStageFlowering: 'مرحلة الإزهار',
    growthStageFruiting: 'مرحلة الإثمار',
    growthStageMaturity: 'مرحلة النضج',
  };

  // ===== أيام الأسبوع =====
  static const Map<int, String> weekDayNames = {
    1: 'الاثنين',
    2: 'الثلاثاء',
    3: 'الأربعاء',
    4: 'الخميس',
    5: 'الجمعة',
    6: 'السبت',
    7: 'الأحد',
  };

  // ===== وحدات القياس =====
  static const String unitPercentage = '%';
  static const String unitCelsius = '°C';
  static const String unitLiters = 'لتر';
  static const String unitMinutes = 'دقيقة';
  static const String unitHours = 'ساعة';
  static const String unitDays = 'يوم';

  // ===== ألوان النظام =====
  static const Map<String, int> systemColors = {
    irrigationTypeDrip: 0xFF2196F3, // أزرق
    irrigationTypeSprinkler: 0xFF4CAF50, // أخضر
    irrigationTypeMicro: 0xFFFF9800, // برتقالي
  };

  // ===== أيقونات النظام =====
  static const Map<String, int> systemIcons = {
    irrigationTypeDrip: 0xf043e, // FontAwesome droplet
    irrigationTypeSprinkler: 0xf2cc, // FontAwesome shower
    irrigationTypeMicro: 0xf4d8, // FontAwesome seedling
  };
}

/// ثوابت التحقق من صحة البيانات
class ValidationConstants {
  // ===== حدود النصوص =====
  static const int minSystemNameLength = 3;
  static const int maxSystemNameLength = 50;
  static const int maxDescriptionLength = 200;
  static const int maxNotesLength = 500;

  // ===== حدود القيم الرقمية =====
  static const double minMoistureThreshold = 0.1;
  static const double maxMoistureThreshold = 0.9;
  static const int minIrrigationDuration = 5;
  static const int maxIrrigationDuration = 180;
  static const double minWaterPerDay = 10.0;
  static const double maxWaterPerDay = 2000.0;

  // ===== أنماط التحقق =====
  static const String serialNumberPattern = r'^[A-Z]{2}-\d{5}$';
  static const String phoneNumberPattern = r'^\+?[1-9]\d{1,14}$';
  static const String emailPattern =
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';

  // ===== رسائل التحقق =====
  static const String errorSystemNameRequired = 'اسم النظام مطلوب';
  static const String errorSystemNameTooShort = 'اسم النظام قصير جداً';
  static const String errorSystemNameTooLong = 'اسم النظام طويل جداً';
  static const String errorInvalidSerialNumber = 'الرقم التسلسلي غير صحيح';
  static const String errorInvalidMoistureThreshold = 'عتبة الرطوبة غير صحيحة';
  static const String errorInvalidDuration = 'مدة الري غير صحيحة';
  static const String errorInvalidWaterAmount = 'كمية المياه غير صحيحة';
}

/// ثوابت التنسيق والعرض
class DisplayConstants {
  // ===== أحجام الخطوط =====
  static const double fontSizeSmall = 12.0;
  static const double fontSizeMedium = 14.0;
  static const double fontSizeLarge = 16.0;
  static const double fontSizeXLarge = 18.0;
  static const double fontSizeXXLarge = 20.0;

  // ===== المسافات =====
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;

  // ===== أحجام الأيقونات =====
  static const double iconSizeSmall = 16.0;
  static const double iconSizeMedium = 24.0;
  static const double iconSizeLarge = 32.0;
  static const double iconSizeXLarge = 48.0;

  // ===== أحجام البطاقات =====
  static const double cardElevation = 2.0;
  static const double cardBorderRadius = 12.0;
  static const double buttonBorderRadius = 8.0;

  // ===== أحجام المؤشرات =====
  static const double circularIndicatorRadius = 35.0;
  static const double circularIndicatorLineWidth = 6.0;
  static const double linearIndicatorHeight = 20.0;

  // ===== مدد الرسوم المتحركة =====
  static const int animationDurationShort = 300;
  static const int animationDurationMedium = 500;
  static const int animationDurationLong = 1000;
}
