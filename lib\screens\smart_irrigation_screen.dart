import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';
import 'package:sam03/bloc/irrigation_bloc.dart';
import 'package:sam03/bloc/auth_bloc.dart';
import 'package:sam03/models/app_models.dart';
import 'package:sam03/repositories/irrigation_repository.dart';
import 'package:sam03/core/constants/app_constants.dart';
import 'package:sam03/screens/link_irrigation_system_screen.dart';
import 'package:sam03/screens/irrigation_settings_screen.dart';
import 'package:sam03/screens/irrigation_statistics_screen.dart';
import 'package:sam03/screens/notifications_screen.dart';
import 'package:sam03/services/notification_service.dart';

/// شاشة الري الذكي المحدثة باستخدام BLoC
class SmartIrrigationScreen extends StatelessWidget {
  const SmartIrrigationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        final authState = context.read<AuthBloc>().state;
        String userId = '';

        if (authState is AuthStateAuthenticated) {
          userId = authState.user.id;
        }

        return IrrigationBloc(
          irrigationRepository: IrrigationRepository(),
        )..add(IrrigationEventLoadSystems(userId));
      },
      child: const _SmartIrrigationView(),
    );
  }
}

class _SmartIrrigationView extends StatefulWidget {
  const _SmartIrrigationView();

  @override
  State<_SmartIrrigationView> createState() => _SmartIrrigationViewState();
}

class _SmartIrrigationViewState extends State<_SmartIrrigationView> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الري الذكي'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          Stack(
            children: [
              IconButton(
                icon: const Icon(Icons.notifications),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const NotificationsScreen(),
                    ),
                  );
                },
              ),
              Positioned(
                right: 8,
                top: 8,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 16,
                    minHeight: 16,
                  ),
                  child: Text(
                    '${NotificationService().unreadCount}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              final authState = context.read<AuthBloc>().state;
              if (authState is AuthStateAuthenticated) {
                context.read<IrrigationBloc>().add(
                      IrrigationEventLoadSystems(authState.user.id),
                    );
              }
            },
          ),
        ],
      ),
      body: BlocConsumer<IrrigationBloc, IrrigationState>(
        listener: (context, state) {
          if (state is IrrigationStateError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          } else if (state is IrrigationStateIrrigationStarted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(AppConstants.messageIrrigationStarted),
                backgroundColor: Colors.green,
              ),
            );
          } else if (state is IrrigationStateIrrigationStopped) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(AppConstants.messageIrrigationStopped),
                backgroundColor: Colors.orange,
              ),
            );
          } else if (state is IrrigationStateSensorsUpdated) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(AppConstants.messageSensorsUpdated),
                backgroundColor: Colors.blue,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is IrrigationStateLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (state is IrrigationStateError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'حدث خطأ',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    state.message,
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      final authState = context.read<AuthBloc>().state;
                      if (authState is AuthStateAuthenticated) {
                        context.read<IrrigationBloc>().add(
                              IrrigationEventLoadSystems(authState.user.id),
                            );
                      }
                    },
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          if (state is IrrigationStateSystemsLoaded) {
            if (state.systems.isEmpty) {
              return _buildEmptyState(context);
            }
            return _buildSystemsList(context, state.systems);
          }

          if (state is IrrigationStateSystemSelected ||
              state is IrrigationStateSensorsUpdated ||
              state is IrrigationStateIrrigationStarted ||
              state is IrrigationStateIrrigationStopped) {
            IrrigationSystem? system;
            bool isIrrigating = false;

            if (state is IrrigationStateSystemSelected) {
              system = state.system;
              isIrrigating = state.isIrrigating;
            } else if (state is IrrigationStateSensorsUpdated) {
              system = state.system;
            } else if (state is IrrigationStateIrrigationStarted) {
              system = context.read<IrrigationBloc>().selectedSystem;
              isIrrigating = true;
            } else if (state is IrrigationStateIrrigationStopped) {
              system = context.read<IrrigationBloc>().selectedSystem;
              isIrrigating = false;
            }

            if (system != null) {
              return _buildSystemDetails(context, system, isIrrigating);
            }
          }

          return _buildInitialState(context);
        },
      ),
    );
  }

  /// بناء الحالة الأولية
  Widget _buildInitialState(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            FontAwesomeIcons.seedling,
            size: 64,
            color: Colors.green,
          ),
          SizedBox(height: 16),
          Text(
            'مرحباً بك في نظام الري الذكي',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'جاري تحميل أنظمة الري...',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء حالة عدم وجود أنظمة
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            FontAwesomeIcons.droplet,
            size: 64,
            color: Colors.blue,
          ),
          const SizedBox(height: 16),
          const Text(
            'لا توجد أنظمة ري مسجلة',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'قم بإضافة نظام ري جديد للبدء',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const LinkIrrigationSystemScreen(),
                ),
              );
            },
            icon: const Icon(Icons.link),
            label: const Text('ربط نظام ري'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قائمة الأنظمة
  Widget _buildSystemsList(
      BuildContext context, List<IrrigationSystem> systems) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'أنظمة الري المتاحة',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: systems.length,
            itemBuilder: (context, index) {
              final system = systems[index];
              return _buildSystemCard(context, system);
            },
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة النظام
  Widget _buildSystemCard(BuildContext context, IrrigationSystem system) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getSystemColor(system.type).withOpacity(0.1),
          child: Icon(
            _getSystemIcon(system.type),
            color: _getSystemColor(system.type),
          ),
        ),
        title: Text(
          system.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('النوع: ${_getSystemTypeName(system.type)}'),
            Text('الحالة: ${_getSystemStatusName(system.status)}'),
            Text(
                'رطوبة التربة: ${(system.sensors.soilMoisture * 100).toInt()}%'),
          ],
        ),
        trailing: Icon(
          system.status == AppConstants.irrigationStatusActive
              ? Icons.check_circle
              : Icons.circle_outlined,
          color: system.status == AppConstants.irrigationStatusActive
              ? Colors.green
              : Colors.grey,
        ),
        onTap: () {
          context.read<IrrigationBloc>().add(
                IrrigationEventSelectSystem(system.id),
              );
        },
      ),
    );
  }

  /// بناء تفاصيل النظام (مبسط)
  Widget _buildSystemDetails(
      BuildContext context, IrrigationSystem system, bool isIrrigating) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان النظام
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    system.name,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text('النوع: ${_getSystemTypeName(system.type)}'),
                  Text('الحالة: ${_getSystemStatusName(system.status)}'),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // أزرار التحكم
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: isIrrigating
                      ? null
                      : () {
                          context.read<IrrigationBloc>().add(
                                IrrigationEventStartIrrigation(
                                  systemId: system.id,
                                  trigger: IrrigationTrigger.manual,
                                ),
                              );
                        },
                  icon: Icon(isIrrigating ? Icons.pause : Icons.play_arrow),
                  label: Text(isIrrigating ? 'جاري الري...' : 'بدء الري'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        isIrrigating ? Colors.orange : Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: isIrrigating
                      ? () {
                          context.read<IrrigationBloc>().add(
                                IrrigationEventStopIrrigation(system.id),
                              );
                        }
                      : null,
                  icon: const Icon(Icons.stop),
                  label: const Text('إيقاف الري'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.red,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => IrrigationSettingsScreen(
                          system: system,
                          currentSettings: null, // يمكن تحسينه لاحقاً
                        ),
                      ),
                    );
                  },
                  icon: const Icon(Icons.settings),
                  label: const Text('الإعدادات'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.blue,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => IrrigationStatisticsScreen(
                          system: system,
                        ),
                      ),
                    );
                  },
                  icon: const Icon(Icons.analytics),
                  label: const Text('الإحصائيات'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.purple,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // قراءات الحساسات
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'قراءات الحساسات',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.refresh),
                        onPressed: () {
                          context.read<IrrigationBloc>().add(
                                IrrigationEventUpdateSensors(system.id),
                              );
                        },
                        tooltip: AppConstants.messageSensorsUpdated,
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildSensorIndicator(
                        icon: FontAwesomeIcons.droplet,
                        title: 'رطوبة التربة',
                        value: system.sensors.soilMoisture,
                        color: _getMoistureColor(system.sensors.soilMoisture),
                        unit: '%',
                      ),
                      _buildSensorIndicator(
                        icon: FontAwesomeIcons.temperatureHalf,
                        title: 'درجة الحرارة',
                        value: system.sensors.temperature / 50,
                        color: _getTemperatureColor(system.sensors.temperature),
                        unit: '°C',
                        actualValue: system.sensors.temperature,
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildSensorIndicator(
                        icon: FontAwesomeIcons.water,
                        title: 'منسوب المياه',
                        value: system.sensors.waterLevel,
                        color: _getWaterLevelColor(system.sensors.waterLevel),
                        unit: '%',
                      ),
                      _buildSensorIndicator(
                        icon: FontAwesomeIcons.batteryHalf,
                        title: 'مستوى البطارية',
                        value: system.sensors.batteryLevel,
                        color: _getBatteryColor(system.sensors.batteryLevel),
                        unit: '%',
                      ),
                      _buildSensorIndicator(
                        icon: system.sensors.rainStatus
                            ? FontAwesomeIcons.cloudRain
                            : FontAwesomeIcons.sun,
                        title: 'حالة الطقس',
                        value: system.sensors.rainStatus ? 1.0 : 0.0,
                        color: system.sensors.rainStatus
                            ? Colors.blue
                            : Colors.orange,
                        unit: '',
                        customText: system.sensors.rainStatus ? 'مطر' : 'صافي',
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء مؤشر الحساس
  Widget _buildSensorIndicator({
    required IconData icon,
    required String title,
    required double value,
    required Color color,
    required String unit,
    double? actualValue,
    String? customText,
  }) {
    final displayValue = actualValue ?? (value * 100);
    final displayText = customText ?? displayValue.toStringAsFixed(1);

    return Column(
      children: [
        CircularPercentIndicator(
          radius: 35.0,
          lineWidth: 6.0,
          percent: value.clamp(0.0, 1.0),
          center: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                displayText,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
              if (unit.isNotEmpty)
                Text(
                  unit,
                  style: const TextStyle(fontSize: 10),
                ),
            ],
          ),
          progressColor: color,
          backgroundColor: Colors.grey.shade200,
          circularStrokeCap: CircularStrokeCap.round,
        ),
        const SizedBox(height: 8),
        Text(
          title,
          style: const TextStyle(fontSize: 12),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  // ===== الدوال المساعدة =====

  /// الحصول على لون النظام حسب النوع
  Color _getSystemColor(String type) {
    switch (type) {
      case AppConstants.irrigationTypeDrip:
        return Colors.blue;
      case AppConstants.irrigationTypeSprinkler:
        return Colors.green;
      case AppConstants.irrigationTypeMicro:
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  /// الحصول على أيقونة النظام حسب النوع
  IconData _getSystemIcon(String type) {
    switch (type) {
      case AppConstants.irrigationTypeDrip:
        return FontAwesomeIcons.droplet;
      case AppConstants.irrigationTypeSprinkler:
        return FontAwesomeIcons.shower;
      case AppConstants.irrigationTypeMicro:
        return FontAwesomeIcons.seedling;
      default:
        return FontAwesomeIcons.water;
    }
  }

  /// الحصول على اسم نوع النظام
  String _getSystemTypeName(String type) {
    return AppConstants.irrigationTypeNames[type] ?? 'غير محدد';
  }

  /// الحصول على اسم حالة النظام
  String _getSystemStatusName(String status) {
    return AppConstants.irrigationStatusNames[status] ?? 'غير محدد';
  }

  /// الحصول على لون رطوبة التربة
  Color _getMoistureColor(double value) {
    if (value < AppConstants.moistureThresholdLow) return Colors.red;
    if (value < AppConstants.moistureThresholdMedium) return Colors.orange;
    if (value < AppConstants.moistureThresholdHigh) return Colors.green;
    return Colors.blue;
  }

  /// الحصول على لون درجة الحرارة
  Color _getTemperatureColor(double value) {
    if (value < AppConstants.temperatureThresholdCold) return Colors.blue;
    if (value < AppConstants.temperatureThresholdNormal) return Colors.green;
    if (value < AppConstants.temperatureThresholdWarm) return Colors.orange;
    return Colors.red;
  }

  /// الحصول على لون منسوب المياه
  Color _getWaterLevelColor(double value) {
    if (value < AppConstants.waterLevelThresholdLow) return Colors.red;
    if (value < AppConstants.waterLevelThresholdMedium) return Colors.orange;
    return Colors.blue;
  }

  /// الحصول على لون البطارية
  Color _getBatteryColor(double value) {
    if (value < AppConstants.batteryThresholdLow) return Colors.red;
    if (value < AppConstants.batteryThresholdMedium) return Colors.orange;
    return Colors.green;
  }
}
