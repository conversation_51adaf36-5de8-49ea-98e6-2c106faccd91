import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:sam03/models/app_models.dart';
import 'package:sam03/repositories/irrigation_repository.dart';
import 'package:sam03/services/unified_notification_service.dart';

// ===== الأحداث (Events) =====

/// الحدث الأساسي لنظام الري
abstract class IrrigationEvent extends Equatable {
  const IrrigationEvent();

  @override
  List<Object?> get props => [];
}

/// تحميل أنظمة الري
class IrrigationEventLoadSystems extends IrrigationEvent {
  final String userId;

  const IrrigationEventLoadSystems(this.userId);

  @override
  List<Object?> get props => [userId];
}

/// تحديد نظام الري النشط
class IrrigationEventSelectSystem extends IrrigationEvent {
  final String systemId;

  const IrrigationEventSelectSystem(this.systemId);

  @override
  List<Object?> get props => [systemId];
}

/// تحديث بيانات الحساسات
class IrrigationEventUpdateSensors extends IrrigationEvent {
  final String systemId;

  const IrrigationEventUpdateSensors(this.systemId);

  @override
  List<Object?> get props => [systemId];
}

/// بدء الري
class IrrigationEventStartIrrigation extends IrrigationEvent {
  final String systemId;
  final IrrigationTrigger trigger;
  final int? duration; // بالدقائق، null للمدة الافتراضية

  const IrrigationEventStartIrrigation({
    required this.systemId,
    required this.trigger,
    this.duration,
  });

  @override
  List<Object?> get props => [systemId, trigger, duration];
}

/// إيقاف الري
class IrrigationEventStopIrrigation extends IrrigationEvent {
  final String systemId;

  const IrrigationEventStopIrrigation(this.systemId);

  @override
  List<Object?> get props => [systemId];
}

/// تحديث إعدادات الري
class IrrigationEventUpdateSettings extends IrrigationEvent {
  final IrrigationSettings settings;

  const IrrigationEventUpdateSettings(this.settings);

  @override
  List<Object?> get props => [settings];
}

/// تحميل سجلات الري
class IrrigationEventLoadRecords extends IrrigationEvent {
  final String systemId;

  const IrrigationEventLoadRecords(this.systemId);

  @override
  List<Object?> get props => [systemId];
}

/// تحميل إحصائيات الري
class IrrigationEventLoadStats extends IrrigationEvent {
  final String systemId;
  final DateTime startDate;
  final DateTime endDate;

  const IrrigationEventLoadStats({
    required this.systemId,
    required this.startDate,
    required this.endDate,
  });

  @override
  List<Object?> get props => [systemId, startDate, endDate];
}

/// البحث عن نظام ري موجود
class IrrigationEventSearchSystem extends IrrigationEvent {
  final String userId;
  final String fullName;
  final String serialNumber;

  const IrrigationEventSearchSystem({
    required this.userId,
    required this.fullName,
    required this.serialNumber,
  });

  @override
  List<Object?> get props => [userId, fullName, serialNumber];
}

/// ربط نظام ري موجود
class IrrigationEventLinkSystem extends IrrigationEvent {
  final String userId;
  final String systemId;

  const IrrigationEventLinkSystem({
    required this.userId,
    required this.systemId,
  });

  @override
  List<Object?> get props => [userId, systemId];
}

// ===== الحالات (States) =====

/// الحالة الأساسية لنظام الري
abstract class IrrigationState extends Equatable {
  const IrrigationState();

  @override
  List<Object?> get props => [];
}

/// الحالة الأولية
class IrrigationStateInitial extends IrrigationState {}

/// حالة التحميل
class IrrigationStateLoading extends IrrigationState {}

/// حالة تحميل أنظمة الري بنجاح
class IrrigationStateSystemsLoaded extends IrrigationState {
  final List<IrrigationSystem> systems;
  final String? selectedSystemId;

  const IrrigationStateSystemsLoaded({
    required this.systems,
    this.selectedSystemId,
  });

  @override
  List<Object?> get props => [systems, selectedSystemId];
}

/// حالة تحديد نظام الري
class IrrigationStateSystemSelected extends IrrigationState {
  final IrrigationSystem system;
  final IrrigationSettings? settings;
  final bool isIrrigating;

  const IrrigationStateSystemSelected({
    required this.system,
    this.settings,
    this.isIrrigating = false,
  });

  @override
  List<Object?> get props => [system, settings, isIrrigating];
}

/// حالة تحديث الحساسات
class IrrigationStateSensorsUpdated extends IrrigationState {
  final IrrigationSystem system;
  final SensorData sensorData;

  const IrrigationStateSensorsUpdated({
    required this.system,
    required this.sensorData,
  });

  @override
  List<Object?> get props => [system, sensorData];
}

/// حالة بدء الري
class IrrigationStateIrrigationStarted extends IrrigationState {
  final String systemId;
  final IrrigationRecord record;

  const IrrigationStateIrrigationStarted({
    required this.systemId,
    required this.record,
  });

  @override
  List<Object?> get props => [systemId, record];
}

/// حالة إيقاف الري
class IrrigationStateIrrigationStopped extends IrrigationState {
  final String systemId;
  final IrrigationRecord record;

  const IrrigationStateIrrigationStopped({
    required this.systemId,
    required this.record,
  });

  @override
  List<Object?> get props => [systemId, record];
}

/// حالة تحميل السجلات
class IrrigationStateRecordsLoaded extends IrrigationState {
  final List<IrrigationRecord> records;

  const IrrigationStateRecordsLoaded(this.records);

  @override
  List<Object?> get props => [records];
}

/// حالة تحميل الإحصائيات
class IrrigationStateStatsLoaded extends IrrigationState {
  final Map<String, dynamic> stats;

  const IrrigationStateStatsLoaded(this.stats);

  @override
  List<Object?> get props => [stats];
}

/// حالة تحديث الإعدادات
class IrrigationStateSettingsUpdated extends IrrigationState {
  final IrrigationSettings settings;

  const IrrigationStateSettingsUpdated(this.settings);

  @override
  List<Object?> get props => [settings];
}

/// حالة العثور على نظام
class IrrigationStateSystemFound extends IrrigationState {
  final IrrigationSystem system;

  const IrrigationStateSystemFound(this.system);

  @override
  List<Object?> get props => [system];
}

/// حالة ربط نظام بنجاح
class IrrigationStateSystemLinked extends IrrigationState {
  final IrrigationSystem system;

  const IrrigationStateSystemLinked(this.system);

  @override
  List<Object?> get props => [system];
}

/// حالة الخطأ
class IrrigationStateError extends IrrigationState {
  final String message;

  const IrrigationStateError(this.message);

  @override
  List<Object?> get props => [message];
}

// ===== BLoC =====

/// BLoC لإدارة نظام الري الذكي
class IrrigationBloc extends Bloc<IrrigationEvent, IrrigationState> {
  final IrrigationRepository irrigationRepository;
  final UnifiedNotificationService _notificationService;

  // متغيرات لتتبع الحالة الحالية
  final List<IrrigationSystem> _systems = [];
  String? _selectedSystemId; // تتبع نظام الري النشط
  final Map<String, bool> _irrigationStatus = {}; // تتبع حالة الري لكل نظام

  IrrigationBloc({
    required this.irrigationRepository,
    UnifiedNotificationService? notificationService,
  })  : _notificationService =
            notificationService ?? UnifiedNotificationService(),
        super(IrrigationStateInitial()) {
    on<IrrigationEventLoadSystems>(_onLoadSystems);
    on<IrrigationEventSelectSystem>(_onSelectSystem);
    on<IrrigationEventUpdateSensors>(_onUpdateSensors);
    on<IrrigationEventStartIrrigation>(_onStartIrrigation);
    on<IrrigationEventStopIrrigation>(_onStopIrrigation);
    on<IrrigationEventUpdateSettings>(_onUpdateSettings);
    on<IrrigationEventLoadRecords>(_onLoadRecords);
    on<IrrigationEventLoadStats>(_onLoadStats);
    on<IrrigationEventSearchSystem>(_onSearchSystem);
    on<IrrigationEventLinkSystem>(_onLinkSystem);
  }

  /// تحميل أنظمة الري
  Future<void> _onLoadSystems(
    IrrigationEventLoadSystems event,
    Emitter<IrrigationState> emit,
  ) async {
    try {
      emit(IrrigationStateLoading());

      _systems.addAll(
          await irrigationRepository.getIrrigationSystems(event.userId));

      emit(IrrigationStateSystemsLoaded(
        systems: _systems,
        selectedSystemId: _selectedSystemId,
      ));
    } catch (e) {
      emit(IrrigationStateError('فشل في تحميل أنظمة الري: ${e.toString()}'));
    }
  }

  /// تحديد نظام الري النشط
  Future<void> _onSelectSystem(
    IrrigationEventSelectSystem event,
    Emitter<IrrigationState> emit,
  ) async {
    try {
      emit(IrrigationStateLoading());

      _selectedSystemId = event.systemId;

      final system =
          await irrigationRepository.getIrrigationSystem(event.systemId);
      if (system == null) {
        emit(const IrrigationStateError('لم يتم العثور على النظام المحدد'));
        return;
      }

      final settings =
          await irrigationRepository.getIrrigationSettings(event.systemId);
      final isIrrigating = _irrigationStatus[event.systemId] ?? false;

      emit(IrrigationStateSystemSelected(
        system: system,
        settings: settings,
        isIrrigating: isIrrigating,
      ));
    } catch (e) {
      emit(IrrigationStateError('فشل في تحديد النظام: ${e.toString()}'));
    }
  }

  /// تحديث بيانات الحساسات
  Future<void> _onUpdateSensors(
    IrrigationEventUpdateSensors event,
    Emitter<IrrigationState> emit,
  ) async {
    try {
      final sensorData =
          await irrigationRepository.simulateSensorUpdate(event.systemId);

      final system =
          await irrigationRepository.getIrrigationSystem(event.systemId);
      if (system != null) {
        emit(IrrigationStateSensorsUpdated(
          system: system,
          sensorData: sensorData,
        ));
      }
    } catch (e) {
      emit(IrrigationStateError('فشل في تحديث الحساسات: ${e.toString()}'));
    }
  }

  /// بدء الري
  Future<void> _onStartIrrigation(
    IrrigationEventStartIrrigation event,
    Emitter<IrrigationState> emit,
  ) async {
    try {
      final system =
          await irrigationRepository.getIrrigationSystem(event.systemId);
      if (system == null) {
        emit(const IrrigationStateError('لم يتم العثور على النظام المحدد'));
        return;
      }

      final settings =
          await irrigationRepository.getIrrigationSettings(event.systemId);
      final duration = event.duration ?? settings?.defaultDuration ?? 30;

      // إنشاء سجل ري جديد
      final record = await irrigationRepository.addIrrigationRecord(
        systemId: event.systemId,
        startTime: DateTime.now(),
        duration: duration,
        waterUsed: duration * 5.0, // تقدير: 5 لتر في الدقيقة
        trigger: event.trigger,
        sensorDataAtStart: system.sensors.toMap(),
      );

      // تحديث حالة الري
      _irrigationStatus[event.systemId] = true;

      // إرسال إشعار بدء الري
      _notificationService.notifyIrrigationStarted(system, event.trigger);

      emit(IrrigationStateIrrigationStarted(
        systemId: event.systemId,
        record: record,
      ));

      // محاكاة إيقاف الري تلقائياً بعد المدة المحددة
      Future.delayed(const Duration(seconds: 5), () {
        add(IrrigationEventStopIrrigation(event.systemId));
      });
    } catch (e) {
      emit(IrrigationStateError('فشل في بدء الري: ${e.toString()}'));
    }
  }

  /// إيقاف الري
  Future<void> _onStopIrrigation(
    IrrigationEventStopIrrigation event,
    Emitter<IrrigationState> emit,
  ) async {
    try {
      // تحديث حالة الري
      _irrigationStatus[event.systemId] = false;

      // في التطبيق الحقيقي، سنحدث سجل الري بوقت الانتهاء الفعلي
      final now = DateTime.now();

      // محاكاة سجل ري مكتمل
      final record = await irrigationRepository.addIrrigationRecord(
        systemId: event.systemId,
        startTime: now.subtract(const Duration(minutes: 5)),
        endTime: now,
        duration: 5,
        waterUsed: 25.0,
        trigger: IrrigationTrigger.manual,
        notes: 'تم إيقاف الري يدوياً',
      );

      // إرسال إشعار إيقاف الري
      final system =
          await irrigationRepository.getIrrigationSystem(event.systemId);
      if (system != null) {
        _notificationService.notifyIrrigationStopped(system, record.waterUsed);
      }

      emit(IrrigationStateIrrigationStopped(
        systemId: event.systemId,
        record: record,
      ));
    } catch (e) {
      emit(IrrigationStateError('فشل في إيقاف الري: ${e.toString()}'));
    }
  }

  /// تحديث إعدادات الري
  Future<void> _onUpdateSettings(
    IrrigationEventUpdateSettings event,
    Emitter<IrrigationState> emit,
  ) async {
    try {
      await irrigationRepository.saveIrrigationSettings(event.settings);
      emit(IrrigationStateSettingsUpdated(event.settings));
    } catch (e) {
      emit(IrrigationStateError('فشل في تحديث الإعدادات: ${e.toString()}'));
    }
  }

  /// تحميل سجلات الري
  Future<void> _onLoadRecords(
    IrrigationEventLoadRecords event,
    Emitter<IrrigationState> emit,
  ) async {
    try {
      emit(IrrigationStateLoading());
      final records =
          await irrigationRepository.getIrrigationRecords(event.systemId);
      emit(IrrigationStateRecordsLoaded(records));
    } catch (e) {
      emit(IrrigationStateError('فشل في تحميل السجلات: ${e.toString()}'));
    }
  }

  /// تحميل إحصائيات الري
  Future<void> _onLoadStats(
    IrrigationEventLoadStats event,
    Emitter<IrrigationState> emit,
  ) async {
    try {
      emit(IrrigationStateLoading());
      final stats = await irrigationRepository.getWaterUsageStats(
        event.systemId,
        startDate: event.startDate,
        endDate: event.endDate,
      );
      emit(IrrigationStateStatsLoaded(stats));
    } catch (e) {
      emit(IrrigationStateError('فشل في تحميل الإحصائيات: ${e.toString()}'));
    }
  }

  /// البحث عن نظام ري موجود
  Future<void> _onSearchSystem(
    IrrigationEventSearchSystem event,
    Emitter<IrrigationState> emit,
  ) async {
    try {
      emit(IrrigationStateLoading());

      final system = await irrigationRepository.searchIrrigationSystem(
        userId: event.userId,
        fullName: event.fullName,
        serialNumber: event.serialNumber,
      );

      if (system != null) {
        emit(IrrigationStateSystemFound(system));
      } else {
        emit(IrrigationStateError(
            'لم يتم العثور على النظام أو البيانات غير صحيحة'));
      }
    } catch (e) {
      emit(IrrigationStateError('فشل في البحث عن النظام: ${e.toString()}'));
    }
  }

  /// ربط نظام ري موجود
  Future<void> _onLinkSystem(
    IrrigationEventLinkSystem event,
    Emitter<IrrigationState> emit,
  ) async {
    try {
      emit(IrrigationStateLoading());

      final system = await irrigationRepository.linkIrrigationSystem(
        userId: event.userId,
        systemId: event.systemId,
      );

      // إضافة النظام للقائمة المحلية
      _systems.add(system);

      emit(IrrigationStateSystemLinked(system));
    } catch (e) {
      emit(IrrigationStateError('فشل في ربط النظام: ${e.toString()}'));
    }
  }

  // ===== دوال مساعدة =====

  /// التحقق من حالة الري لنظام محدد
  bool isSystemIrrigating(String systemId) {
    return _irrigationStatus[systemId] ?? false;
  }

  /// الحصول على النظام المحدد حالياً
  IrrigationSystem? get selectedSystem {
    if (_selectedSystemId == null) return null;
    try {
      return _systems.firstWhere((system) => system.id == _selectedSystemId);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على جميع الأنظمة
  List<IrrigationSystem> get systems => List.unmodifiable(_systems);
}
