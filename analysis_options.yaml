# This file configures the analyzer, which statically analyzes Dart code to
# check for errors, warnings, and lints.
#
# The issues identified by the analyzer are surfaced in the UI of Dart-enabled
# IDEs (https://dart.dev/tools#ides-and-editors). The analyzer can also be
# invoked from the command line by running `flutter analyze`.

# The following line activates a set of recommended lints for Flutter apps,
# packages, and plugins designed to encourage good coding practices.
include: package:flutter_lints/flutter.yaml

linter:
  rules:
    # Performance optimizations
    avoid_unnecessary_containers: true
    sized_box_for_whitespace: true

    # Disable some strict rules for development to improve performance
    use_key_in_widget_constructors: false
    prefer_const_constructors: false
    prefer_const_literals_to_create_immutables: false
    prefer_const_constructors_in_immutables: false

    # Enable helpful rules
    avoid_empty_else: true
    avoid_returning_null_for_future: true
    cancel_subscriptions: true
    close_sinks: true

analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
    - "**/generated_plugin_registrant.dart"
    - "build/**"
    - ".dart_tool/**"

  errors:
    # Treat some errors as warnings to improve development experience
    missing_required_param: warning
    always_declare_return_types: warning

  language:
    strict-casts: false
    strict-inference: false
    strict-raw-types: false
