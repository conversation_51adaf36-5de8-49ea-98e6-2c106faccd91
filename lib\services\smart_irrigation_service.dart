import 'dart:math';
import 'package:sam03/models/app_models.dart';
import 'package:sam03/core/constants/app_constants.dart';

/// خدمة الري الذكي والتوصيات المبنية على الذكاء الاصطناعي
class SmartIrrigationService {
  static final SmartIrrigationService _instance = SmartIrrigationService._internal();
  factory SmartIrrigationService() => _instance;
  SmartIrrigationService._internal();

  /// تحليل حالة النظام وتقديم توصيات ذكية
  SmartRecommendation analyzeSystemAndRecommend(
    IrrigationSystem system,
    IrrigationSettings? settings,
    List<IrrigationRecord> recentRecords,
    WeatherData? weatherData,
  ) {
    final analysis = _analyzeSystemConditions(system, settings, recentRecords, weatherData);
    final recommendations = _generateRecommendations(analysis);
    final irrigationNeeded = _shouldIrrigate(analysis);
    final optimizedDuration = _calculateOptimalDuration(analysis);

    return SmartRecommendation(
      systemId: system.id,
      timestamp: DateTime.now(),
      irrigationNeeded: irrigationNeeded,
      recommendedDuration: optimizedDuration,
      confidence: analysis.confidence,
      reasons: recommendations,
      priority: _calculatePriority(analysis),
      estimatedWaterUsage: optimizedDuration * AppConstants.defaultWaterFlowRate,
      nextCheckTime: DateTime.now().add(Duration(hours: _getNextCheckInterval(analysis))),
    );
  }

  /// تحليل شامل لحالة النظام
  SystemAnalysis _analyzeSystemConditions(
    IrrigationSystem system,
    IrrigationSettings? settings,
    List<IrrigationRecord> recentRecords,
    WeatherData? weatherData,
  ) {
    final sensors = system.sensors;
    final now = DateTime.now();
    
    // تحليل رطوبة التربة
    final moistureScore = _analyzeMoisture(sensors.soilMoisture, settings);
    
    // تحليل الطقس
    final weatherScore = _analyzeWeather(weatherData, sensors);
    
    // تحليل التوقيت
    final timeScore = _analyzeTime(now);
    
    // تحليل السجلات الأخيرة
    final historyScore = _analyzeHistory(recentRecords, now);
    
    // تحليل حالة النبات
    final plantScore = _analyzePlantCondition(system, settings);
    
    // حساب الثقة الإجمالية
    final confidence = _calculateConfidence([
      moistureScore,
      weatherScore,
      timeScore,
      historyScore,
      plantScore,
    ]);

    return SystemAnalysis(
      moistureLevel: sensors.soilMoisture,
      moistureScore: moistureScore,
      weatherScore: weatherScore,
      timeScore: timeScore,
      historyScore: historyScore,
      plantScore: plantScore,
      confidence: confidence,
      temperature: sensors.temperature,
      humidity: sensors.humidity,
      lastIrrigation: _getLastIrrigationTime(recentRecords),
      waterLevel: sensors.waterLevel,
      batteryLevel: sensors.batteryLevel,
    );
  }

  /// تحليل رطوبة التربة
  double _analyzeMoisture(double moisture, IrrigationSettings? settings) {
    final threshold = settings?.moistureThreshold ?? AppConstants.defaultMoistureThreshold;
    final hysteresis = settings?.hysteresisMargin ?? AppConstants.defaultHysteresisMargin;
    
    if (moisture < threshold - hysteresis) {
      return 1.0; // يحتاج ري بشدة
    } else if (moisture < threshold) {
      return 0.7; // يحتاج ري
    } else if (moisture < threshold + hysteresis) {
      return 0.3; // قد يحتاج ري
    } else {
      return 0.0; // لا يحتاج ري
    }
  }

  /// تحليل الطقس
  double _analyzeWeather(WeatherData? weather, SensorData sensors) {
    if (weather == null) {
      // الاعتماد على حساس المطر المحلي
      return sensors.rainStatus ? -0.5 : 0.2;
    }

    double score = 0.0;
    
    // تحليل الأمطار
    if (weather.isRaining || weather.rainProbability > 0.7) {
      score -= 0.8; // تقليل الحاجة للري
    } else if (weather.rainProbability > 0.3) {
      score -= 0.3;
    }
    
    // تحليل درجة الحرارة
    if (weather.temperature > 35) {
      score += 0.4; // زيادة الحاجة للري في الحر
    } else if (weather.temperature > 30) {
      score += 0.2;
    }
    
    // تحليل الرطوبة
    if (weather.humidity < 0.3) {
      score += 0.3; // زيادة الحاجة في الجو الجاف
    }
    
    // تحليل الرياح
    if (weather.windSpeed > 20) {
      score += 0.2; // الرياح تزيد التبخر
    }

    return score.clamp(-1.0, 1.0);
  }

  /// تحليل التوقيت
  double _analyzeTime(DateTime now) {
    final hour = now.hour;
    
    // أفضل أوقات الري: الصباح الباكر (5-8) والمساء (17-19)
    if ((hour >= 5 && hour <= 8) || (hour >= 17 && hour <= 19)) {
      return 0.3;
    }
    // أوقات مقبولة: الليل (20-23) والصباح المتأخر (9-10)
    else if ((hour >= 20 && hour <= 23) || (hour >= 9 && hour <= 10)) {
      return 0.1;
    }
    // أوقات غير مناسبة: منتصف النهار (11-16)
    else if (hour >= 11 && hour <= 16) {
      return -0.4;
    }
    // أوقات محايدة: الليل المتأخر والفجر
    else {
      return 0.0;
    }
  }

  /// تحليل السجلات الأخيرة
  double _analyzeHistory(List<IrrigationRecord> records, DateTime now) {
    if (records.isEmpty) return 0.2;
    
    final lastRecord = records.first;
    final timeSinceLastIrrigation = now.difference(lastRecord.startTime);
    
    // إذا تم الري مؤخراً (أقل من 6 ساعات)
    if (timeSinceLastIrrigation.inHours < 6) {
      return -0.6;
    }
    // إذا تم الري في آخر 12 ساعة
    else if (timeSinceLastIrrigation.inHours < 12) {
      return -0.3;
    }
    // إذا تم الري في آخر 24 ساعة
    else if (timeSinceLastIrrigation.inHours < 24) {
      return 0.0;
    }
    // إذا لم يتم الري لأكثر من يوم
    else if (timeSinceLastIrrigation.inDays >= 1) {
      return 0.4;
    }
    // إذا لم يتم الري لأكثر من يومين
    else if (timeSinceLastIrrigation.inDays >= 2) {
      return 0.7;
    }
    
    return 0.0;
  }

  /// تحليل حالة النبات
  double _analyzePlantCondition(IrrigationSystem system, IrrigationSettings? settings) {
    // هذا تحليل مبسط - يمكن تطويره ليشمل نوع النبات ومرحلة النمو
    double score = 0.0;
    
    // تحليل نوع النظام
    switch (system.type) {
      case AppConstants.irrigationTypeDrip:
        score += 0.1; // أنظمة التنقيط تحتاج ري أكثر تكراراً
        break;
      case AppConstants.irrigationTypeSprinkler:
        score -= 0.1; // أنظمة الرش تغطي مساحة أكبر
        break;
      case AppConstants.irrigationTypeMicro:
        score += 0.2; // الري الدقيق للنباتات الحساسة
        break;
    }
    
    return score;
  }

  /// حساب الثقة الإجمالية
  double _calculateConfidence(List<double> scores) {
    final validScores = scores.where((s) => s.isFinite).toList();
    if (validScores.isEmpty) return 0.5;
    
    final average = validScores.reduce((a, b) => a + b) / validScores.length;
    final variance = validScores.map((s) => pow(s - average, 2)).reduce((a, b) => a + b) / validScores.length;
    
    // كلما قل التباين، زادت الثقة
    return (1.0 - variance.clamp(0.0, 1.0)).clamp(0.1, 1.0);
  }

  /// تحديد ما إذا كان النظام يحتاج للري
  bool _shouldIrrigate(SystemAnalysis analysis) {
    final totalScore = analysis.moistureScore + 
                     analysis.weatherScore + 
                     analysis.timeScore + 
                     analysis.historyScore + 
                     analysis.plantScore;
    
    return totalScore > 0.5 && analysis.confidence > 0.6;
  }

  /// حساب المدة المثلى للري
  int _calculateOptimalDuration(SystemAnalysis analysis) {
    int baseDuration = AppConstants.defaultIrrigationDuration;
    
    // تعديل المدة حسب رطوبة التربة
    if (analysis.moistureLevel < 0.2) {
      baseDuration = (baseDuration * 1.5).round();
    } else if (analysis.moistureLevel < 0.3) {
      baseDuration = (baseDuration * 1.2).round();
    } else if (analysis.moistureLevel > 0.6) {
      baseDuration = (baseDuration * 0.7).round();
    }
    
    // تعديل المدة حسب درجة الحرارة
    if (analysis.temperature > 35) {
      baseDuration = (baseDuration * 1.3).round();
    } else if (analysis.temperature > 30) {
      baseDuration = (baseDuration * 1.1).round();
    }
    
    // تعديل المدة حسب مستوى المياه
    if (analysis.waterLevel < 0.3) {
      baseDuration = (baseDuration * 0.8).round(); // تقليل المدة لتوفير المياه
    }
    
    return baseDuration.clamp(5, 120); // بين 5 دقائق و 2 ساعة
  }

  /// تحديد أولوية التوصية
  RecommendationPriority _calculatePriority(SystemAnalysis analysis) {
    if (analysis.moistureLevel < 0.2 && analysis.confidence > 0.8) {
      return RecommendationPriority.critical;
    } else if (analysis.moistureLevel < 0.3 && analysis.confidence > 0.7) {
      return RecommendationPriority.high;
    } else if (analysis.moistureLevel < 0.4 && analysis.confidence > 0.6) {
      return RecommendationPriority.medium;
    } else {
      return RecommendationPriority.low;
    }
  }

  /// تحديد فترة الفحص التالية
  int _getNextCheckInterval(SystemAnalysis analysis) {
    if (analysis.moistureLevel < 0.3) {
      return 2; // فحص كل ساعتين
    } else if (analysis.moistureLevel < 0.5) {
      return 4; // فحص كل 4 ساعات
    } else {
      return 8; // فحص كل 8 ساعات
    }
  }

  /// الحصول على وقت آخر ري
  DateTime? _getLastIrrigationTime(List<IrrigationRecord> records) {
    if (records.isEmpty) return null;
    return records.first.startTime;
  }

  /// توليد التوصيات النصية
  List<String> _generateRecommendations(SystemAnalysis analysis) {
    final recommendations = <String>[];
    
    if (analysis.moistureLevel < 0.3) {
      recommendations.add('رطوبة التربة منخفضة - يُنصح بالري فوراً');
    }
    
    if (analysis.temperature > 35) {
      recommendations.add('درجة الحرارة مرتفعة - زيادة مدة الري');
    }
    
    if (analysis.waterLevel < 0.3) {
      recommendations.add('مستوى المياه منخفض - تحقق من الخزان');
    }
    
    if (analysis.batteryLevel < 0.2) {
      recommendations.add('البطارية منخفضة - يرجى الشحن');
    }
    
    final now = DateTime.now();
    if (now.hour >= 11 && now.hour <= 16) {
      recommendations.add('تجنب الري في منتصف النهار - انتظر حتى المساء');
    }
    
    if (analysis.lastIrrigation != null) {
      final timeSince = now.difference(analysis.lastIrrigation!);
      if (timeSince.inHours < 6) {
        recommendations.add('تم الري مؤخراً - لا حاجة للري الآن');
      }
    }
    
    return recommendations;
  }
}
